
import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Edit,
  Trash2,
  Video,
  Mic,
  ListChecks,
  Star,
  Calendar,
  PenTool,
  ChevronDown,
  Heading1,
  Heading2,
  Type,
  CheckCircle,
  ClipboardList,
  Box,
  GripVertical,
  Image,
  Youtube,
  Link,
  FileText,
  File,
  Code,
  Boxes,
  TextQuote,
  CircleCheck,
  Clock,
  Timer,
  Phone,
  Hash,
  CheckSquare,
  TextCursor,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ContentComponent, DroppedItem, ContentMode } from "@/types/draggable";
import ComponentEditor from "../editors/ComponentEditor";
import { Badge } from "@/components/ui/badge";


interface ComponentRendererProps {
  item: DroppedItem;
  index: number;
  onRemove: (id: string) => void;
  onUpdate: (id: string, data: ContentComponent) => void;
  onReorder?: (sourceIndex: number, targetIndex: number) => void;
  isDraggable?: boolean;
  isInvalid?: boolean;
}

const ComponentRenderer: React.FC<ComponentRendererProps> = ({
  item,
  index,
  onRemove,
  onUpdate,
  onReorder,
  isDraggable = false,
  isInvalid = false,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);

  const getComponentIcon = () => {
    const iconProps = { className: "h-5 w-5" };

    switch (item.type) {
      case 'header':
        return <Heading1 {...iconProps} />;
      case 'section-header':
        return <Heading2 {...iconProps} />;
      case 'text-body':
        return <Type {...iconProps} />;
      case 'text':
        return <FileText {...iconProps} />;

      case 'image':
        return <Image {...iconProps} />;
      case 'video':
        return <Video {...iconProps} />;
      case 'youtube':
        return <Youtube {...iconProps} />;
      case 'weblink':
        return <Link {...iconProps} />;
      case 'audio':
        return <Mic {...iconProps} />;
      case 'attachment':
        return <File {...iconProps} />;
      case 'embed':
        return <Code {...iconProps} />;
      case 'scorm':
        return <Boxes {...iconProps} />;
      case 'webgl':
        return <Boxes {...iconProps} />;

      case 'feedback-video':
        return <Video {...iconProps} />;
      case 'feedback-audio':
        return <Mic {...iconProps} />;
      case 'mcq':
        return <ListChecks {...iconProps} />;
      case 'textbox':
        return <TextQuote {...iconProps} />;
      case 'text-input':
        return <TextCursor {...iconProps} />;
      case 'option':
        return <CircleCheck {...iconProps} />;
      case 'dropdown':
        return <ChevronDown {...iconProps} />;
      case 'sign':
        return <PenTool {...iconProps} />;
      case 'checkbox':
        return <CheckSquare {...iconProps} />;
      case 'star':
        return <Star {...iconProps} />;
      case 'date':
        return <Calendar {...iconProps} />;
      case 'time':
        return <Clock {...iconProps} />;
      case 'duration':
        return <Timer {...iconProps} />;
      case 'phone':
        return <Phone {...iconProps} />;
      case 'alphanumeric':
        return <Hash {...iconProps} />;

      case 'checkpoint':
        return <CheckCircle {...iconProps} />;
      case 'checkpoint-group':
        return <ClipboardList {...iconProps} />;

      default:
        return <Box {...iconProps} />;
    }
  };

  const getComponentLabel = () => {
    switch (item.type) {
      case 'header': return 'Header';
      case 'section-header': return 'Section Header';
      case 'text-body': return 'Text Body';
      case 'text': return 'Text';

      case 'image': return 'Image';
      case 'video': return 'Video';
      case 'youtube': return 'YouTube';
      case 'weblink': return 'Web Link';
      case 'audio': return 'Audio';
      case 'attachment': return 'Attachment';
      case 'embed': return 'Embed';
      case 'scorm': return 'SCORM';
      case 'webgl': return 'WebGL';

      case 'mcq': return 'Multiple Choice Question';
      case 'textbox': return 'Text Input';
      case 'text-input': return 'Text Input';
      case 'option': return 'Option';
      case 'dropdown': return 'Dropdown';
      case 'sign': return 'Signature';
      case 'checkbox': return 'Checkbox';
      case 'star': return 'Star Rating';
      case 'date': return 'Date';
      case 'time': return 'Time';
      case 'duration': return 'Duration';
      case 'phone': return 'Phone Number';
      case 'alphanumeric': return 'Alphanumeric';

      case 'feedback-video': return 'Video Upload';
      case 'feedback-audio': return 'Audio Upload';

      case 'checkpoint': return 'Checkpoint';
      case 'checkpoint-group': return 'Checkpoint Group';

      default: return 'Component';
    }
  };

  const handleUpdate = (updatedData: ContentComponent) => {
    onUpdate(item.id, updatedData);
    setIsEditing(false);
  };

  const handleRemove = () => {
    onRemove(item.id);
  };

  const formatFileSize = (bytes: number | undefined) => {
    if (!bytes) return 'Unknown size';
    const kb = bytes / 1024;
    if (kb < 1024) {
      return `${Math.round(kb)} KB`;
    } else {
      return `${(kb / 1024).toFixed(2)} MB`;
    }
  };

  const renderPreview = () => {
    // Enhanced preview implementation
    switch (item.type) {
      case 'header':
        const headerData = item.data as ContentComponent & { text?: string, level?: number };
        const HeaderTag = `h${headerData.level || 1}` as keyof JSX.IntrinsicElements;
        return headerData.text ? (
          <HeaderTag className={`font-bold ${
            headerData.level === 1 ? 'text-3xl' :
            headerData.level === 2 ? 'text-2xl' :
            headerData.level === 3 ? 'text-xl' :
            headerData.level === 4 ? 'text-lg' :
            headerData.level === 5 ? 'text-base' :
            'text-sm'
          }`}>
            {headerData.text}
          </HeaderTag>
        ) : (
          <div className="text-muted-foreground italic">No header text added yet</div>
        );

      case 'section-header':
        const sectionHeaderData = item.data as ContentComponent & { text?: string, level?: number };
        const SectionHeaderTag = `h${sectionHeaderData.level || 2}` as keyof JSX.IntrinsicElements;
        return sectionHeaderData.text ? (
          <SectionHeaderTag className={`font-semibold ${
            sectionHeaderData.level === 1 ? 'text-2xl' :
            sectionHeaderData.level === 2 ? 'text-xl' :
            sectionHeaderData.level === 3 ? 'text-lg' :
            sectionHeaderData.level === 4 ? 'text-base' :
            sectionHeaderData.level === 5 ? 'text-sm' :
            'text-xs'
          }`}>
            {sectionHeaderData.text}
          </SectionHeaderTag>
        ) : (
          <div className="text-muted-foreground italic">No section header text added yet</div>
        );

      case 'text-body':
        const textBodyData = item.data as ContentComponent & { content?: string };
        return textBodyData.content ? (
          <div className="prose prose-sm max-w-none text-sm leading-relaxed">
            {textBodyData.content.split('\n').map((line, index) => (
              <p key={index} className="mb-2 last:mb-0">{line}</p>
            ))}
          </div>
        ) : (
          <div className="text-muted-foreground italic">No text content added yet</div>
        );

      case 'text':
        const textData = item.data as ContentComponent & { content?: string };
        return textData.content ? (
          <div className="prose prose-sm max-w-none" dangerouslySetInnerHTML={{ __html: textData.content }} />
        ) : (
          <div className="text-muted-foreground italic">No content added yet</div>
        );

      case 'image':
        const imageData = item.data as ContentComponent & { url?: string, title?: string, src?: string, alt?: string };
        return imageData.url || imageData.src ? (
          <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center overflow-hidden">
            <div className="text-xs text-muted-foreground">Image: {imageData.title || imageData.alt || "Untitled"}</div>
          </div>
        ) : (
          <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
            <Image className="h-8 w-8 text-muted-foreground opacity-50" />
          </div>
        );

      case 'video':
        const videoData = item.data as ContentComponent & { url?: string, title?: string, src?: string };
        return videoData.url || videoData.src ? (
          <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center overflow-hidden">
            <div className="text-xs text-muted-foreground">Video: {videoData.title || "Untitled"}</div>
          </div>
        ) : (
          <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
            <Video className="h-8 w-8 text-muted-foreground opacity-50" />
          </div>
        );

      case 'youtube':
        const youtubeData = item.data as ContentComponent & { url?: string, videoId?: string };
        return youtubeData.url || youtubeData.videoId ? (
          <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center overflow-hidden">
            <div className="text-xs text-muted-foreground">YouTube Video</div>
          </div>
        ) : (
          <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
            <Youtube className="h-8 w-8 text-muted-foreground opacity-50" />
          </div>
        );

      case 'weblink':
        const weblinkData = item.data as ContentComponent & { url?: string, title?: string };
        return weblinkData.url ? (
          <div className="border rounded-md p-3 bg-white dark:bg-slate-800">
            <div className="flex items-center gap-2">
              <Link className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">{weblinkData.title || "Web Link"}</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1 truncate">{weblinkData.url}</p>
          </div>
        ) : (
          <div className="border rounded-md p-3 bg-slate-100 dark:bg-slate-700 flex items-center justify-center">
            <Link className="h-8 w-8 text-muted-foreground opacity-50" />
          </div>
        );

      case 'audio':
        const audioData = item.data as ContentComponent & { url?: string, title?: string, src?: string };
        return audioData.url || audioData.src ? (
          <div className="border rounded-md p-3 bg-white dark:bg-slate-800">
            <div className="flex items-center gap-2">
              <Mic className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">{audioData.title || "Audio File"}</span>
            </div>
          </div>
        ) : (
          <div className="border rounded-md p-3 bg-slate-100 dark:bg-slate-700 flex items-center justify-center">
            <Mic className="h-8 w-8 text-muted-foreground opacity-50" />
          </div>
        );

      case 'attachment':
        const attachmentData = item.data as ContentComponent & { fileName?: string, fileSize?: number, url?: string };
        return attachmentData.fileName || attachmentData.url ? (
          <div className="border rounded-md p-3 bg-white dark:bg-slate-800">
            <div className="flex items-center gap-2">
              <File className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">{attachmentData.fileName || "Attachment"}</span>
            </div>
            {attachmentData.fileSize && (
              <p className="text-xs text-muted-foreground mt-1">
                Size: {formatFileSize(attachmentData.fileSize)}
              </p>
            )}
          </div>
        ) : (
          <div className="border rounded-md p-3 bg-slate-100 dark:bg-slate-700 flex items-center justify-center">
            <File className="h-8 w-8 text-muted-foreground opacity-50" />
          </div>
        );

      case 'embed':
        const embedData = item.data as ContentComponent & { code?: string };
        return embedData.code ? (
          <div className="border rounded-md p-3 bg-white dark:bg-slate-800">
            <div className="flex items-center gap-2">
              <Code className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Embedded Content</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">Custom embed code</p>
          </div>
        ) : (
          <div className="border rounded-md p-3 bg-slate-100 dark:bg-slate-700 flex items-center justify-center">
            <Code className="h-8 w-8 text-muted-foreground opacity-50" />
          </div>
        );

      case 'scorm':
        const scormData = item.data as ContentComponent & { fileName?: string, fileSize?: number, src?: string };
        return scormData.fileName || scormData.src ? (
          <div className="border rounded-md p-3 bg-white dark:bg-slate-800">
            <div className="flex items-center gap-2">
              <Boxes className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">{scormData.fileName || "SCORM Package"}</span>
            </div>
            {scormData.fileSize && (
              <p className="text-xs text-muted-foreground mt-1">
                Size: {formatFileSize(scormData.fileSize)}
              </p>
            )}
          </div>
        ) : (
          <div className="border rounded-md p-3 bg-slate-100 dark:bg-slate-700 flex items-center justify-center">
            <Boxes className="h-8 w-8 text-muted-foreground opacity-50" />
          </div>
        );

      case 'webgl':
        const webglData = item.data as ContentComponent & { fileName?: string, fileSize?: number, src?: string };
        return webglData.fileName || webglData.src ? (
          <div className="border rounded-md p-3 bg-white dark:bg-slate-800">
            <div className="flex items-center gap-2">
              <Boxes className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">{webglData.fileName || "WebGL Content"}</span>
            </div>
            {webglData.fileSize && (
              <p className="text-xs text-muted-foreground mt-1">
                Size: {formatFileSize(webglData.fileSize)}
              </p>
            )}
          </div>
        ) : (
          <div className="border rounded-md p-3 bg-slate-100 dark:bg-slate-700 flex items-center justify-center">
            <Boxes className="h-8 w-8 text-muted-foreground opacity-50" />
          </div>
        );

      case 'mcq':
        const mcqData = item.data as ContentComponent & {
          question?: string,
          options?: { id: string; text: string }[],
          allowMultiple?: boolean,
          required?: boolean
        };
        return (
          <div className="space-y-4">
            {mcqData.question ? (
              <div>
                <p className="font-medium text-sm mb-3 leading-relaxed">
                  {mcqData.question}
                  {mcqData.required && <span className="text-red-500 ml-1">*</span>}
                </p>
                {mcqData.options && mcqData.options.length > 0 ? (
                  <div className="space-y-2">
                    {mcqData.options.map((option, index) => (
                      <div key={option.id} className="flex items-center space-x-3 p-2 rounded-md hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors">
                        <div className={`w-4 h-4 border-2 border-primary flex-shrink-0 ${
                          mcqData.allowMultiple
                            ? 'rounded-sm'
                            : 'rounded-full'
                        } bg-white dark:bg-slate-900`}>
                          {/* Visual indicator for checkbox vs radio */}
                          {mcqData.allowMultiple && (
                            <div className="w-full h-full flex items-center justify-center">
                              <div className="w-2 h-2 bg-transparent"></div>
                            </div>
                          )}
                        </div>
                        <label className="text-sm cursor-pointer flex-1 select-none">
                          {option.text}
                        </label>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-muted-foreground italic text-sm">No answer options added yet</div>
                )}
                <div className="text-xs text-muted-foreground mt-2">
                  {mcqData.allowMultiple ? 'Multiple selections allowed' : 'Single selection only'}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-16 bg-muted/50 rounded-md">
                <ListChecks className="h-6 w-6 text-muted-foreground" />
                <span className="ml-2 text-muted-foreground">No question added yet</span>
              </div>
            )}
          </div>
        );

      case 'textbox':
        const textboxData = item.data as ContentComponent & { label?: string, placeholder?: string, required?: boolean };
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {textboxData.label || "Text input"}
              {textboxData.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="border rounded-md p-2 text-sm text-muted-foreground bg-white dark:bg-slate-800">
              {textboxData.placeholder || "Enter text here..."}
            </div>
          </div>
        );

      case 'text-input':
        const textInputData = item.data as ContentComponent & { label?: string, placeholder?: string, required?: boolean };
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {textInputData.label || "Text input"}
              {textInputData.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="border rounded-md p-2 text-sm text-muted-foreground bg-white dark:bg-slate-800">
              {textInputData.placeholder || "Enter text here..."}
            </div>
          </div>
        );

      case 'option':
        const optionData = item.data as ContentComponent & { label?: string, options?: string[], required?: boolean };
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {optionData.label || "Select option"}
              {optionData.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="space-y-2">
              {(optionData.options || ["Option 1", "Option 2"]).map((option, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="w-4 h-4 border border-primary rounded-full flex-shrink-0"></div>
                  <span className="text-sm">{option}</span>
                </div>
              ))}
            </div>
          </div>
        );

      case 'dropdown':
        const dropdownData = item.data as ContentComponent & { label?: string, options?: string[], required?: boolean };
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {dropdownData.label || "Select from dropdown"}
              {dropdownData.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="border rounded-md p-2 text-sm text-muted-foreground bg-white dark:bg-slate-800 flex items-center justify-between">
              <span>Choose an option...</span>
              <ChevronDown className="h-4 w-4" />
            </div>
          </div>
        );

      case 'sign':
        const signData = item.data as ContentComponent & { label?: string, required?: boolean };
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {signData.label || "Signature"}
              {signData.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="border rounded-md p-4 text-sm text-muted-foreground bg-white dark:bg-slate-800 flex items-center justify-center h-20">
              <PenTool className="h-6 w-6 mr-2" />
              <span>Signature pad</span>
            </div>
          </div>
        );

      case 'checkbox':
        const checkboxData = item.data as ContentComponent & { label?: string, required?: boolean };
        return (
          <div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border border-primary rounded-sm flex-shrink-0"></div>
              <span className="text-sm">{checkboxData.label || "Checkbox option"}</span>
              {checkboxData.required && <span className="text-red-500 ml-1">*</span>}
            </div>
          </div>
        );

      case 'star':
        const starData = item.data as ContentComponent & { label?: string, maxStars?: number, required?: boolean };
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {starData.label || "Star rating"}
              {starData.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="flex gap-1">
              {Array.from({ length: starData.maxStars || 5 }).map((_, index) => (
                <Star key={index} className="h-5 w-5 text-muted-foreground" />
              ))}
            </div>
          </div>
        );

      case 'date':
        const dateData = item.data as ContentComponent & { label?: string, required?: boolean };
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {dateData.label || "Date"}
              {dateData.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="border rounded-md p-2 text-sm text-muted-foreground bg-white dark:bg-slate-800 flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>Select date...</span>
            </div>
          </div>
        );

      case 'time':
        const timeData = item.data as ContentComponent & { label?: string, required?: boolean };
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {timeData.label || "Time"}
              {timeData.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="border rounded-md p-2 text-sm text-muted-foreground bg-white dark:bg-slate-800 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span>Select time...</span>
            </div>
          </div>
        );

      case 'duration':
        const durationData = item.data as ContentComponent & { label?: string, required?: boolean };
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {durationData.label || "Duration"}
              {durationData.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="border rounded-md p-2 text-sm text-muted-foreground bg-white dark:bg-slate-800 flex items-center gap-2">
              <Timer className="h-4 w-4" />
              <span>Enter duration...</span>
            </div>
          </div>
        );

      case 'phone':
        const phoneData = item.data as ContentComponent & { label?: string, required?: boolean };
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {phoneData.label || "Phone number"}
              {phoneData.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="border rounded-md p-2 text-sm text-muted-foreground bg-white dark:bg-slate-800 flex items-center gap-2">
              <Phone className="h-4 w-4" />
              <span>Enter phone number...</span>
            </div>
          </div>
        );

      case 'alphanumeric':
        const alphanumericData = item.data as ContentComponent & { label?: string, required?: boolean };
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {alphanumericData.label || "Alphanumeric input"}
              {alphanumericData.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="border rounded-md p-2 text-sm text-muted-foreground bg-white dark:bg-slate-800 flex items-center gap-2">
              <Hash className="h-4 w-4" />
              <span>Enter alphanumeric value...</span>
            </div>
          </div>
        );

      case 'feedback-video':
        const feedbackVideoData = item.data as ContentComponent & { label?: string, required?: boolean };
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {feedbackVideoData.label || "Video upload"}
              {feedbackVideoData.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="border rounded-md p-4 text-sm text-muted-foreground bg-white dark:bg-slate-800 flex items-center justify-center h-20">
              <Video className="h-6 w-6 mr-2" />
              <span>Upload video file</span>
            </div>
          </div>
        );

      case 'feedback-audio':
        const feedbackAudioData = item.data as ContentComponent & { label?: string, required?: boolean };
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {feedbackAudioData.label || "Audio upload"}
              {feedbackAudioData.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="border rounded-md p-4 text-sm text-muted-foreground bg-white dark:bg-slate-800 flex items-center justify-center h-20">
              <Mic className="h-6 w-6 mr-2" />
              <span>Upload audio file</span>
            </div>
          </div>
        );

      case 'checkpoint':
        const checkpointData = item.data as ContentComponent & { text?: string, description?: string };
        return (
          <div className="space-y-2">
            {checkpointData.text ? (
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-sm">{checkpointData.text}</p>
                  {checkpointData.description && (
                    <p className="text-xs text-muted-foreground mt-1">{checkpointData.description}</p>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-16 bg-muted/50 rounded-md">
                <CheckCircle className="h-6 w-6 text-muted-foreground" />
                <span className="ml-2 text-muted-foreground">No checkpoint text added</span>
              </div>
            )}
          </div>
        );

      case 'checkpoint-group':
        const checkpointGroupData = item.data as ContentComponent & {
          title?: string,
          description?: string,
          checkpoints?: { id: string; text: string; description?: string }[]
        };
        return (
          <div className="space-y-4">
            {checkpointGroupData.title && (
              <div>
                <h4 className="font-semibold text-sm">{checkpointGroupData.title}</h4>
                {checkpointGroupData.description && (
                  <p className="text-xs text-muted-foreground mt-1">{checkpointGroupData.description}</p>
                )}
              </div>
            )}
            {checkpointGroupData.checkpoints && checkpointGroupData.checkpoints.length > 0 ? (
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {checkpointGroupData.checkpoints.map((checkpoint) => (
                  <div key={checkpoint.id} className="space-y-2">
                    <p className="text-sm font-medium">{checkpoint.text}</p>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border border-primary rounded-full flex-shrink-0"></div>
                        <span className="text-sm">Yes</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border border-primary rounded-full flex-shrink-0"></div>
                        <span className="text-sm">No</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border border-primary rounded-full flex-shrink-0"></div>
                        <span className="text-sm">Not-applicable</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-16 bg-muted/50 rounded-md">
                <ClipboardList className="h-6 w-6 text-muted-foreground" />
                <span className="ml-2 text-muted-foreground">
                  {checkpointGroupData.title ? 'No questions added' : 'No checkpoint group configured'}
                </span>
              </div>
            )}
          </div>
        );

      default:
        return (
          <div className="flex items-center text-muted-foreground">
            {getComponentIcon()}
            <span className="ml-2">{getComponentLabel()} placeholder</span>
          </div>
        );
    }
  };

  // Determine if this is a communicate or feedback component
  const isCommunicateComponent = !item.type.startsWith('feedback-') &&
    !['mcq', 'textbox', 'text-input', 'option', 'dropdown', 'sign', 'checkbox', 'star', 'date', 'time', 'duration', 'phone', 'alphanumeric', 'checkpoint', 'checkpoint-group'].includes(item.type);

  const componentMode: ContentMode = isCommunicateComponent ? 'communicate' : 'feedback';

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent) => {
    if (!isDraggable || !onReorder) return;

    e.dataTransfer.setData("componentId", item.id);
    e.dataTransfer.effectAllowed = "move";
    setIsDragging(true);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (!isDraggable || !onReorder) return;

    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    if (!isDraggable || !onReorder) return;

    e.preventDefault();
    setIsDragOver(false);

    const sourceId = e.dataTransfer.getData("componentId");
    if (sourceId && sourceId !== item.id) {
      // This will be handled by the DropZone components instead
      // ComponentRenderer drop is mainly for visual feedback
      return;
    }
  };

  return (
    <Card
      className={`p-5 fade-in shadow-sm border-slate-200 dark:border-slate-700 ${isDraggable ? 'cursor-move' : ''}
        ${isDragging ? 'opacity-50 rotate-1 scale-[0.98]' : ''}
        ${isDragOver ? 'border-primary border-2 bg-primary/5 dark:bg-primary/10 scale-[1.01]' : ''}
        transition-all duration-200 hover:shadow-md
      `}
      draggable={isDraggable}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <div className="flex justify-between items-start mb-3">
        <div className="flex items-center">
          {isDraggable && (
            <div className="text-muted-foreground mr-2 cursor-grab p-1 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md transition-colors">
              <GripVertical className="h-5 w-5" />
            </div>
          )}
          <div className="text-primary mr-3 p-2 bg-primary/10 rounded-md">
            {getComponentIcon()}
          </div>
          <div className="flex items-center">
            <h3 className="font-medium">{getComponentLabel()}</h3>
            <Badge variant={componentMode === 'communicate' ? 'default' : 'secondary'} className="ml-2 text-xs">
              {componentMode === 'communicate' ? 'Content' : 'Form'}
            </Badge>
          </div>
        </div>
        <div className="space-x-2">
          <Dialog open={isEditing} onOpenChange={setIsEditing}>
            <DialogTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-primary transition-colors">
                <Edit className="h-4 w-4" />
                <span className="sr-only">Edit</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Edit {getComponentLabel()}</DialogTitle>
              </DialogHeader>
              <ComponentEditor
                component={item.data}
                onSave={handleUpdate}
                onCancel={() => setIsEditing(false)}
              />
            </DialogContent>
          </Dialog>

          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-red-50 dark:hover:bg-red-900/30 hover:text-red-500 transition-colors"
            onClick={handleRemove}
          >
            <Trash2 className="h-4 w-4 text-destructive" />
            <span className="sr-only">Remove</span>
          </Button>
        </div>
      </div>

      <div className="mt-3 bg-slate-50 dark:bg-slate-900/50 p-3 rounded-md">
        {renderPreview()}
      </div>
    </Card>
  );
};

export default ComponentRenderer;
