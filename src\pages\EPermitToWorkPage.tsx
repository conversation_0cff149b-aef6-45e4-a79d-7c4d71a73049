import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import PageHeader from '@/components/common/PageHeader';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { FileText, Plus, Search, Clock, AlertTriangle } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

const EPermitToWorkPage = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <PageHeader
          title="ePermit to Work"
          description="Define workflows and controls for managing high-risk work through digital permits"
        />
        <Button className="flex items-center gap-1" onClick={() => 
          toast({ 
            title: "Create Permit", 
            description: "Permit creation functionality will be implemented soon." 
          })
        }>
          <Plus className="h-4 w-4" /> Create Permit
        </Button>
      </div>

      <Tabs defaultValue="active-permits" className="w-full">
        <TabsList className="grid w-full md:w-[600px] grid-cols-3">
          <TabsTrigger value="active-permits">Active Permits</TabsTrigger>
          <TabsTrigger value="pending-approval">Pending Approval</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
        </TabsList>
        
        <TabsContent value="active-permits" className="space-y-4">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search active permits..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">PTW-2023-0001</CardTitle>
                    <CardDescription>Hot Work - Welding</CardDescription>
                  </div>
                  <Badge className="bg-green-500">Active</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Location:</span>
                    <span className="font-medium">Building A, Floor 2</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Issued to:</span>
                    <span className="font-medium">John Smith</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Valid until:</span>
                    <span className="font-medium flex items-center">
                      <Clock className="h-3 w-3 mr-1 text-amber-500" />
                      May 20, 2023 (5:00 PM)
                    </span>
                  </div>
                  <div className="pt-2">
                    <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
                      <FileText className="h-3 w-3" /> View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">PTW-2023-0002</CardTitle>
                    <CardDescription>Confined Space Entry</CardDescription>
                  </div>
                  <Badge className="bg-green-500">Active</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Location:</span>
                    <span className="font-medium">Storage Tank #3</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Issued to:</span>
                    <span className="font-medium">Sarah Johnson</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Valid until:</span>
                    <span className="font-medium flex items-center">
                      <AlertTriangle className="h-3 w-3 mr-1 text-red-500" />
                      May 19, 2023 (2:30 PM)
                    </span>
                  </div>
                  <div className="pt-2">
                    <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
                      <FileText className="h-3 w-3" /> View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="pending-approval" className="space-y-4">
          <div className="bg-card rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Pending Approval</h2>
            <p className="text-muted-foreground">
              This tab will display permits waiting for your approval.
            </p>
          </div>
        </TabsContent>
        
        <TabsContent value="completed" className="space-y-4">
          <div className="bg-card rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Completed Permits</h2>
            <p className="text-muted-foreground">
              This tab will display a history of completed work permits.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EPermitToWorkPage;
