import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

export interface ChartExportOptions {
  title: string;
  data: any[];
  chartElement?: HTMLElement | null;
}

export class ChartUtils {
  // Refresh data function
  static refreshChartData(chartTitle: string, onRefresh?: () => void) {
    console.log(`Refreshing data for ${chartTitle}`);
    if (onRefresh) {
      onRefresh();
    } else {
      alert(`${chartTitle} data refreshed successfully!`);
    }
  }

  // Export chart as PNG
  static async exportChartAsPNG(options: ChartExportOptions): Promise<void> {
    const { title, chartElement } = options;
    
    if (!chartElement) {
      alert('Chart element not found');
      return;
    }

    try {
      const canvas = await html2canvas(chartElement, {
        scale: 2,
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true,
      });

      const link = document.createElement('a');
      link.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_chart.png`;
      link.href = canvas.toDataURL();
      link.click();
    } catch (error) {
      console.error('Error exporting PNG:', error);
      alert('Failed to export chart as PNG');
    }
  }

  // Export chart as PDF
  static async exportChartAsPDF(options: ChartExportOptions): Promise<void> {
    const { title, chartElement } = options;
    
    if (!chartElement) {
      alert('Chart element not found');
      return;
    }

    try {
      const canvas = await html2canvas(chartElement, {
        scale: 2,
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true,
      });

      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      });

      const imgData = canvas.toDataURL('image/png');
      const imgWidth = 297; // A4 landscape width in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
      pdf.save(`${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_chart.pdf`);
    } catch (error) {
      console.error('Error exporting PDF:', error);
      alert('Failed to export chart as PDF');
    }
  }

  // Export chart data as CSV
  static exportChartDataAsCSV(options: ChartExportOptions): void {
    const { title, data } = options;
    
    if (!data || data.length === 0) {
      alert('No data available to export');
      return;
    }

    try {
      const headers = Object.keys(data[0] || {});
      const csvContent = [
        [`Chart Data - ${title}`],
        [`Generated: ${new Date().toLocaleDateString()}`],
        [],
        headers,
        ...data.map(row => headers.map(header => row[header]))
      ].map(row => row.join(',')).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_data.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert('Failed to export chart data as CSV');
    }
  }

  // Share chart function
  static async shareChart(title: string): Promise<void> {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Chart: ${title}`,
          text: `Check out this chart: ${title}`,
          url: window.location.href
        });
      } catch (error) {
        console.error('Error sharing:', error);
        ChartUtils.copyChartLink(title);
      }
    } else {
      ChartUtils.copyChartLink(title);
    }
  }

  // Copy chart link to clipboard
  static copyChartLink(title: string): void {
    navigator.clipboard.writeText(window.location.href).then(() => {
      alert(`Link to ${title} copied to clipboard!`);
    }).catch(() => {
      alert('Failed to copy link to clipboard');
    });
  }
}
