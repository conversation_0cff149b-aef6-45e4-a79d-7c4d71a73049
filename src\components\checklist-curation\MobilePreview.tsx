import React from "react";
import { <PERSON>alog, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { DroppedItem } from "@/types/draggable";
import { X } from "lucide-react";
import { Button } from "../ui/button";
import ComponentPreview from "./ComponentPreview";

interface MobilePreviewProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  items: DroppedItem[];
}

const MobilePreview: React.FC<MobilePreviewProps> = ({
  open,
  onOpenChange,
  items,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[400px] p-0 overflow-hidden h-[80vh] max-h-[700px]">
        <DialogHeader className="p-4 border-b">
          <div className="flex justify-between items-center">
            <DialogTitle>Mobile Preview</DialogTitle>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => onOpenChange(false)}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogDescription>
            Preview how your content will appear on mobile devices
          </DialogDescription>
        </DialogHeader>
        
        <div className="p-4 bg-slate-100 dark:bg-slate-900 h-full">
          <div className="mx-auto bg-white dark:bg-slate-800 rounded-t-xl overflow-hidden shadow-xl border border-slate-300 dark:border-slate-600 max-w-[320px]">
            {/* Mobile status bar */}
            <div className="h-6 bg-slate-800 flex items-center justify-between px-4">
              <div className="text-white text-[10px]">9:41</div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-2 bg-white rounded-sm"></div>
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <div className="text-white text-[10px]">100%</div>
              </div>
            </div>
            
            {/* Mobile content */}
            <ScrollArea className="h-[calc(80vh-120px)] max-h-[580px]">
              <div className="p-4 space-y-4">
                {items.length > 0 ? (
                  items.map((item) => (
                    <ComponentPreview key={item.id} item={item} />
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>No content to preview</p>
                  </div>
                )}
              </div>
            </ScrollArea>
            
            {/* Mobile home indicator */}
            <div className="h-6 bg-white dark:bg-slate-800 flex items-center justify-center border-t border-slate-200 dark:border-slate-700">
              <div className="w-1/3 h-1 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MobilePreview;
