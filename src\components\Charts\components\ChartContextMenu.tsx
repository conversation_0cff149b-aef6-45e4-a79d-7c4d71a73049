import React from 'react';
import { More<PERSON><PERSON>zon<PERSON>, RefreshCw, Maximize2, Download, Share2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChartUtils, ChartExportOptions } from '../utils/chartUtils';

interface ChartContextMenuProps {
  title: string;
  data: any[];
  chartElement?: HTMLElement | null;
  onRefresh?: () => void;
  onFullScreen?: () => void;
  className?: string;
}

const ChartContextMenu: React.FC<ChartContextMenuProps> = ({
  title,
  data,
  chartElement,
  onRefresh,
  onFullScreen,
  className = ""
}) => {
  const exportOptions: ChartExportOptions = {
    title,
    data,
    chartElement
  };

  const handleRefresh = () => {
    ChartUtils.refreshChartData(title, onRefresh);
  };

  const handleExportPNG = () => {
    ChartUtils.exportChartAsPNG(exportOptions);
  };

  const handleExportPDF = () => {
    ChartUtils.exportChartAsPDF(exportOptions);
  };

  const handleExportCSV = () => {
    ChartUtils.exportChartDataAsCSV(exportOptions);
  };

  const handleShare = () => {
    ChartUtils.shareChart(title);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className={`p-1.5 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-gray-500 hover:text-gray-700 ${className}`}>
          <MoreHorizontal className="w-4 h-4" />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem
          className="flex items-center space-x-2 cursor-pointer"
          onSelect={handleRefresh}
        >
          <RefreshCw className="w-4 h-4" />
          <span>Refresh Data</span>
        </DropdownMenuItem>
        {onFullScreen && (
          <DropdownMenuItem
            className="flex items-center space-x-2 cursor-pointer"
            onSelect={onFullScreen}
          >
            <Maximize2 className="w-4 h-4" />
            <span>View Full Screen</span>
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="flex items-center space-x-2 cursor-pointer"
          onSelect={handleExportPNG}
        >
          <Download className="w-4 h-4" />
          <span>Export as PNG</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          className="flex items-center space-x-2 cursor-pointer"
          onSelect={handleExportPDF}
        >
          <Download className="w-4 h-4" />
          <span>Export as PDF</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          className="flex items-center space-x-2 cursor-pointer"
          onSelect={handleExportCSV}
        >
          <Download className="w-4 h-4" />
          <span>Export Data (CSV)</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="flex items-center space-x-2 cursor-pointer"
          onSelect={handleShare}
        >
          <Share2 className="w-4 h-4" />
          <span>Share Chart</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ChartContextMenu;
