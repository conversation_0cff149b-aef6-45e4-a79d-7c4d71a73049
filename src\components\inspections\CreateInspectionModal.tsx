import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import AllFilterLocation from '@/components/observations/AllFilterLocation';
import apiService from '@/services/apiService';
import { API_BASE_URL } from '@/constants/index';

// API endpoints
const GET_USER_ROLE_BY_MODE = `${API_BASE_URL}/users/get_users`;
const ADMINDROPDOWNS = `${API_BASE_URL}/dropdowns`;


interface CreateInspectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  accessToken: string;
}

interface OptionType {
  label: string;
  value: string;
  version?: string;
  category?: string;
}

interface NewInspection {
  name: string;
  scheduledDate: Date | null;
  inspectionCategory: string;
  maskId: string;
  dueDate: Date | null;
  status: string;
  checklistVersion: string;
  inspectorId: string;
  assignedById: string;
  checklistId: string;
  locationOneId: string;
  locationTwoId: string;
  locationThreeId: string;
  locationFourId: string;
  locationFiveId: string;
  locationSixId: string;
}

const CreateInspectionModal: React.FC<CreateInspectionModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  accessToken
}) => {
  const { toast } = useToast();
  const { user } = useSelector((state: RootState) => state.auth);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showValidationErrors, setShowValidationErrors] = useState(false);

  // Form state
  const [newInspection, setNewInspection] = useState<NewInspection>({
    name: '',
    scheduledDate: null,
    inspectionCategory: '',
    maskId: '',
    dueDate: null,
    status: '',
    checklistVersion: '',
    inspectorId: '',
    assignedById: '',
    checklistId: '',
    locationOneId: '',
    locationTwoId: '',
    locationThreeId: '',
    locationFourId: '',
    locationFiveId: '',
    locationSixId: '',
  });

  // Dropdown options
  const [inspectionCategories, setInspectionCategories] = useState<OptionType[]>([]);
  const [checklistOptions, setChecklistOptions] = useState<OptionType[]>([]);
  const [filteredChecklistOptions, setFilteredChecklistOptions] = useState<OptionType[]>([]);
  const [inspectorOptions, setInspectorOptions] = useState<OptionType[]>([]);

  // Fetch inspection categories from dropdown API
  const fetchDropdownData = useCallback(async (maskId: string, setState: React.Dispatch<React.SetStateAction<OptionType[]>>) => {
    try {
      const uriString = {
        where: { maskId },
        include: [{ relation: "dropdownItems" }],
      };
      const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await apiService.get(url);
      const data = response[0]?.dropdownItems.map((item: any) => ({
        label: item.name,
        value: item.name,
      })) || [];
      setState(data);
    } catch (error) {
      console.error(`Error fetching ${maskId} list:`, error);
    }
  }, [accessToken]);

  // Fetch checklist options
  const fetchChecklistOptions = useCallback(async () => {
    try {
      const uriString = {
        where: { status: 'Published' },
      };
      const response = await apiService.get(`/checklists?filter=${encodeURIComponent(JSON.stringify(uriString))}`);
      const options = response.map((item: any) => ({
        label: `${item.name} (v${item.version})`,
        value: item.id,
        version: item.version,
        category: item.category || '',
      }));
      setChecklistOptions(options);
    } catch (error) {
      console.error('Error fetching checklist options:', error);
    }
  }, [accessToken]);

  // Fetch inspector options
  const fetchInspectorOptions = useCallback(async () => {
    try {
      const response = await apiService.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: newInspection.locationOneId || '',
        locationTwoId: newInspection.locationTwoId || '',
        locationThreeId: newInspection.locationThreeId || '',
        locationFourId: newInspection.locationFourId || '',
        mode: 'ins_inspector'
      });

      const options = response.map((user: any) => ({
        label: `${user.firstName}`,
        value: user.id,
      }));
      setInspectorOptions(options);
    } catch (error) {
      console.error('Error fetching inspector options:', error);
    }
  }, [accessToken,newInspection.inspectionCategory]);

  // Handle location filter
  const handleFilter = (
    locationOneId: string, 
    locationTwoId?: string,
    locationThreeId?: string,
    locationFourId?: string,
    locationFiveId?: string,
    locationSixId?: string
  ) => {
    setNewInspection((prev) => ({
      ...prev,
      locationOneId,
      locationTwoId: locationTwoId || '',
      locationThreeId: locationThreeId || '',
      locationFourId: locationFourId || '',
      locationFiveId: locationFiveId || '',
      locationSixId: locationSixId || '',
    }));
  };

  // Load data when modal opens
  useEffect(() => {
    if (isOpen && accessToken) {
      setIsLoading(true);
      Promise.all([
        fetchDropdownData('ins_category', setInspectionCategories),
        fetchChecklistOptions(),
        fetchInspectorOptions(),
      ]).finally(() => {
        setIsLoading(false);
      });
    }
  }, [isOpen, accessToken, fetchDropdownData, fetchChecklistOptions, fetchInspectorOptions]);

  // Filter checklists based on selected category
  useEffect(() => {
    if (newInspection.inspectionCategory) {
      const filtered = checklistOptions.filter(opt =>
        opt.category.toLowerCase().includes(newInspection.inspectionCategory.toLowerCase())
      );
      setFilteredChecklistOptions(filtered);
    } else {
      setFilteredChecklistOptions(checklistOptions);
    }
  }, [newInspection.inspectionCategory, checklistOptions]);

  // Validation function
  const validateForm = () => {
    const errors = [];

    if (!newInspection.locationOneId) {
      errors.push('Location is required');
    }
    if (!newInspection.scheduledDate) {
      errors.push('Scheduled Date is required');
    }
    if (!newInspection.dueDate) {
      errors.push('Due Date is required');
    }
    if (!newInspection.inspectionCategory) {
      errors.push('Inspection Category is required');
    }
    if (!newInspection.checklistId) {
      errors.push('Checklist is required');
    }
    if (!newInspection.inspectorId) {
      errors.push('Inspector is required');
    }

    return errors;
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    const validationErrors = validateForm();

    if (validationErrors.length > 0) {
      setShowValidationErrors(true);
      toast({
        title: "Validation Error",
        description: validationErrors.join(', '),
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const inspectionData = {
        ...newInspection,
        scheduledDate: newInspection.scheduledDate?.toISOString(),
        dueDate: newInspection.dueDate?.toISOString(),
        status: 'Scheduled',
        assignedById: user?.id || '', // Use current user ID from Redux store
      };

      await apiService.post('/inspections', inspectionData);

      onSuccess();
      // Reset form and validation errors
      setNewInspection({
        name: '',
        scheduledDate: null,
        inspectionCategory: '',
        maskId: '',
        dueDate: null,
        status: '',
        checklistVersion: '',
        inspectorId: '',
        assignedById: '',
        checklistId: '',
        locationOneId: '',
        locationTwoId: '',
        locationThreeId: '',
        locationFourId: '',
        locationFiveId: '',
        locationSixId: '',
      });
      setShowValidationErrors(false);
    } catch (error) {
      console.error('Error creating inspection:', error);
      toast({
        title: "Error",
        description: "Failed to create inspection. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset validation errors when modal closes
  const handleClose = () => {
    setShowValidationErrors(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Inspection</DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading...</span>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Location Selection */}
            <div className="space-y-2">
              <Card className={showValidationErrors && !newInspection.locationOneId ? "border-red-500" : ""}>
                <CardContent className="pt-6">
                  <Label className={showValidationErrors && !newInspection.locationOneId ? "text-red-500" : ""}>
                    Location *
                  </Label>
                  <AllFilterLocation handleFilter={handleFilter} getLocation={newInspection} />
                  {showValidationErrors && !newInspection.locationOneId && (
                    <p className="text-red-500 text-sm mt-2">Location is required</p>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Scheduled Date */}
            <div className="space-y-2">
              <Label className={showValidationErrors && !newInspection.scheduledDate ? "text-red-500" : ""}>
                Scheduled Date *
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !newInspection.scheduledDate && "text-muted-foreground",
                      showValidationErrors && !newInspection.scheduledDate && "border-red-500"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {newInspection.scheduledDate ? format(newInspection.scheduledDate, "PPP") : "Select scheduled date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={newInspection.scheduledDate || undefined}
                    onSelect={(date) => setNewInspection(prev => ({ ...prev, scheduledDate: date || null }))}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {showValidationErrors && !newInspection.scheduledDate && (
                <p className="text-red-500 text-sm">Scheduled Date is required</p>
              )}
            </div>

            {/* Due Date */}
            <div className="space-y-2">
              <Label className={showValidationErrors && !newInspection.dueDate ? "text-red-500" : ""}>
                Due Date *
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !newInspection.dueDate && "text-muted-foreground",
                      showValidationErrors && !newInspection.dueDate && "border-red-500"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {newInspection.dueDate ? format(newInspection.dueDate, "PPP") : "Select due date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={newInspection.dueDate || undefined}
                    onSelect={(date) => setNewInspection(prev => ({ ...prev, dueDate: date || null }))}
                    disabled={(date) => date < (newInspection.scheduledDate || new Date())}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {showValidationErrors && !newInspection.dueDate && (
                <p className="text-red-500 text-sm">Due Date is required</p>
              )}
            </div>

            {/* Inspection Category */}
            <div className="space-y-2">
              <Label className={showValidationErrors && !newInspection.inspectionCategory ? "text-red-500" : ""}>
                Inspection Category *
              </Label>
              <Select
                value={newInspection.inspectionCategory}
                onValueChange={(value) => {
                  setNewInspection(prev => ({
                    ...prev,
                    inspectionCategory: value,
                    checklistId: '',
                    checklistVersion: '',
                  }));
                }}
              >
                <SelectTrigger className={showValidationErrors && !newInspection.inspectionCategory ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select Category" />
                </SelectTrigger>
                <SelectContent>
                  {inspectionCategories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showValidationErrors && !newInspection.inspectionCategory && (
                <p className="text-red-500 text-sm">Inspection Category is required</p>
              )}
            </div>

            {/* Checklist */}
            <div className="space-y-2">
              <Label className={showValidationErrors && !newInspection.checklistId ? "text-red-500" : ""}>
                Checklist *
              </Label>
              <Select
                value={newInspection.checklistId}
                onValueChange={(value) => {
                  const selectedChecklist = filteredChecklistOptions.find(opt => opt.value === value);
                  setNewInspection(prev => ({
                    ...prev,
                    checklistId: value,
                    checklistVersion: selectedChecklist?.version || '',
                  }));
                }}
                disabled={!newInspection.inspectionCategory}
              >
                <SelectTrigger className={showValidationErrors && !newInspection.checklistId ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select Checklist" />
                </SelectTrigger>
                <SelectContent>
                  {filteredChecklistOptions.map((checklist) => (
                    <SelectItem key={checklist.value} value={checklist.value}>
                      {checklist.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showValidationErrors && !newInspection.checklistId && (
                <p className="text-red-500 text-sm">Checklist is required</p>
              )}
            </div>

            {/* Inspector */}
            <div className="space-y-2">
              <Label className={showValidationErrors && !newInspection.inspectorId ? "text-red-500" : ""}>
                Inspector *
              </Label>
              <Select
                value={newInspection.inspectorId}
                onValueChange={(value) => {
                  setNewInspection(prev => ({
                    ...prev,
                    inspectorId: value,
                  }));
                }}
              >
                <SelectTrigger className={showValidationErrors && !newInspection.inspectorId ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select Inspector" />
                </SelectTrigger>
                <SelectContent>
                  {inspectorOptions.map((inspector) => (
                    <SelectItem key={inspector.value} value={inspector.value}>
                      {inspector.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showValidationErrors && !newInspection.inspectorId && (
                <p className="text-red-500 text-sm">Inspector is required</p>
              )}
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading || isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Create Inspection
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateInspectionModal;
