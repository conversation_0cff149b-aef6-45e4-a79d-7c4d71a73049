import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface LoginConfig {
  LOGO: string;
  LOGO_URL?: string;
  LOGIN_BUTTON_TEXT: string;
  COGNITO_REGION: string;
  COGNITO_USER_DOMAIN: string;
  COGNITO_USER_POOL_ID: string;
  COGNITO_USER_APP_CLIENT_ID: string;
  SELECTED_INDUSTRIES: string[];
  COMPANY_NAME?: string;
  COMPANY_DESCRIPTION?: string;
}

export interface UserDetails {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  username: string;
  role: string;
  department?: string;
  position?: string;
  phoneNumber?: string;
  profileImage?: string;
  signInDetails?: {
    loginId: string;
  };
}

interface AuthState {
  isAuthenticated: boolean;
  accessToken: string | null;
  refreshToken: string | null;
  loginConfig: LoginConfig | null;
  user: UserDetails | null;
  isLoading: boolean;
  error: string | null;
}

// Helper function to check if tokens exist in localStorage
const checkForTokens = (): boolean => {
  try {
    const accessToken = localStorage.getItem('access_token');
    const refreshToken = localStorage.getItem('refresh_token');
    return !!(accessToken && refreshToken);
  } catch (error) {
    console.error('Error accessing localStorage:', error);
    return false;
  }
};

const initialState: AuthState = {
  isAuthenticated: checkForTokens(),
  accessToken: localStorage.getItem('access_token'),
  refreshToken: localStorage.getItem('refresh_token'),
  loginConfig: null,
  user: null,
  isLoading: false,
  error: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setLogin: (state) => {
      state.isAuthenticated = true;
    },
    setLogout: (state) => {
      state.isAuthenticated = false;
      state.accessToken = null;
      state.refreshToken = null;
      state.user = null;
      state.error = null;
      // Clear localStorage
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('headerLogoUrl');
      localStorage.removeItem('COGNITO_USER_DOMAIN');
      localStorage.removeItem('COGNITO_USER_APP_CLIENT_ID');
    },
    setTokens: (state, action: PayloadAction<{ accessToken: string; refreshToken: string }>) => {
      state.accessToken = action.payload.accessToken;
      state.refreshToken = action.payload.refreshToken;
      state.isAuthenticated = true;
      localStorage.setItem('access_token', action.payload.accessToken);
      localStorage.setItem('refresh_token', action.payload.refreshToken);
    },
    setLoginConfig: (state, action: PayloadAction<LoginConfig>) => {
      state.loginConfig = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setUser: (state, action: PayloadAction<UserDetails>) => {
      state.user = action.payload;
    },
  },
});

export const { setLogin, setLogout, setTokens, setLoginConfig, setLoading, setError, setUser } = authSlice.actions;
export default authSlice.reducer;
