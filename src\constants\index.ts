// API Base URL
export const API_BASE_URL = 'https://client-api.acuizen.com';

// File handling endpoints
export const FILE_DOWNLOAD = (file: string) => {
  return `${API_BASE_URL}/files/${file}/presigned-url`;
};

export const FILE_DOWNLOAD_WITH_S3 = (bucket: string, fileName: string) => {
  return `${API_BASE_URL}/files/${bucket}/${fileName}/presigned-url`;
};

export const GET_BLOB = `${API_BASE_URL}/files/blob`;

// Observation endpoints
export const OBSERVATION_REPORTS = `${API_BASE_URL}/observation-reports`;

// User endpoints
export const USER_DETAILS = `${API_BASE_URL}/users/me`;

// Services endpoints
export const SERVICES = `${API_BASE_URL}/services`;
export const ASSIGNED_ACTIONS = `${API_BASE_URL}/my-assigned-actions/All`;

// Login config endpoint
export const LOGIN_CONFIG = `${API_BASE_URL}/login-configs`;

// Risk Assessment endpoints
export const RISK_ASSESSMENTS = `${API_BASE_URL}/risk-assessments`;

// User management endpoints
export const EXTERNAL_USERS_URL = `${API_BASE_URL}/users/external`;
export const USERS_URL_WITH_ID = (id: string) => `${API_BASE_URL}/users/${id}`;

// Role assignment endpoints
export const GET_INDIVIDUAL_USER_LOCATION_ROLE_URL = `${API_BASE_URL}/user-location-roles/get-individual-users`;
export const INDIVIDUAL_USER_LOCATION_ROLE_URL = `${API_BASE_URL}/user-location-roles`;

export const USER_LOCATION_ROLE_URL = `${API_BASE_URL}/individual-user-location-roles`;

// Service endpoints (additional)
export const SERVICE_DETAILS = `${API_BASE_URL}/services`;

// Additional user endpoints
export const USERS_URL = `${API_BASE_URL}/users`;

// Department and designation endpoints
export const DEPARTMENT_URL = `${API_BASE_URL}/departments`;
export const DESIGNATION_URL = `${API_BASE_URL}/designations`;

// Other endpoints
export const GMS1_URL = `${API_BASE_URL}/gms1`;
export const WORKING_GROUP_URL = `${API_BASE_URL}/working-groups`;
