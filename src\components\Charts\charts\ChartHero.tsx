
import {
  Composed<PERSON><PERSON>,
  Line,
  Area,
  <PERSON>Axis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';


interface ChartHeroProps {
  selectedMetric: string;
}

const ChartHero = ({ selectedMetric }: ChartHeroProps) => {

  // Sample data for different metrics
  const getChartData = () => {
    if (selectedMetric === 'emissions') {
      return [
        { month: 'Jan', current: 230, target: 250, previous: 280, scope1: 85, scope2: 120, scope3: 25 },
        { month: 'Feb', current: 215, target: 245, previous: 275, scope1: 78, scope2: 115, scope3: 22 },
        { month: 'Mar', current: 235, target: 240, previous: 270, scope1: 82, scope2: 125, scope3: 28 },
        { month: 'Apr', current: 205, target: 235, previous: 265, scope1: 75, scope2: 110, scope3: 20 },
        { month: 'May', current: 222, target: 230, previous: 260, scope1: 80, scope2: 118, scope3: 24 },
        { month: 'Jun', current: 195, target: 225, previous: 255, scope1: 72, scope2: 105, scope3: 18 },
        { month: 'Jul', current: 183, target: 220, previous: 250, scope1: 68, scope2: 100, scope3: 15 },
        { month: 'Aug', current: 189, target: 215, previous: 245, scope1: 70, scope2: 102, scope3: 17 },
        { month: 'Sep', current: 182, target: 210, previous: 240, scope1: 65, scope2: 98, scope3: 19 },
        { month: 'Oct', current: 173, target: 205, previous: 235, scope1: 62, scope2: 95, scope3: 16 },
        { month: 'Nov', current: 166, target: 200, previous: 230, scope1: 60, scope2: 92, scope3: 14 },
        { month: 'Dec', current: 160, target: 195, previous: 225, scope1: 58, scope2: 90, scope3: 12 }
      ];
    }
    // Add other metric data here
    return [];
  };

  const data = getChartData();

  const renderChart = () => {
    return (
      <ResponsiveContainer width="100%" height={400}>
        <ComposedChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <defs>
            <linearGradient id="currentGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
              <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="targetGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#10b981" stopOpacity={0.6}/>
              <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
          <XAxis
            dataKey="month"
            stroke="#64748b"
            fontSize={12}
            tickLine={false}
          />
          <YAxis
            stroke="#64748b"
            fontSize={12}
            tickLine={false}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '12px',
              boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
            }}
          />
          <Legend />

          <Area
            type="monotone"
            dataKey="current"
            fill="url(#currentGradient)"
            stroke="#3b82f6"
            strokeWidth={3}
            name="Current Performance"
          />
          <Area
            type="monotone"
            dataKey="target"
            fill="url(#targetGradient)"
            stroke="#10b981"
            strokeWidth={2}
            strokeDasharray="5 5"
            name="Target"
          />
          <Line
            type="monotone"
            dataKey="previous"
            stroke="#94a3b8"
            strokeWidth={2}
            strokeDasharray="3 3"
            name="Previous Year"
            dot={false}
          />
        </ComposedChart>
      </ResponsiveContainer>
    );
  };

  return (
    <div className="bg-gradient-to-br from-gray-50 to-white rounded-xl p-4 border border-gray-100">
      {renderChart()}
    </div>
  );
};

export default ChartHero;
