
const Header = () => {
  return (
    <header className="fixed top-0 left-0 right-0 bg-white/90 backdrop-blur-md shadow-lg border-b border-gray-200 z-50">
      <div className="w-full px-8 py-4">
        <div className="flex items-center justify-between w-full gap-12">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <img
                src="/Images/Sttgdc.png"
                alt="Company Logo"
                className="h-10 w-auto object-contain"
              />
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  Navigos Sustainability Program Management Admin Platform
                </h1>
                <p className="text-sm text-gray-600">
                  Real-time insights and interactive data visualization
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center">
            <img
              src="/Images/freepik__enhance__46727.png"
              alt="Logo"
              className="h-10 w-auto object-contain"
            />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
