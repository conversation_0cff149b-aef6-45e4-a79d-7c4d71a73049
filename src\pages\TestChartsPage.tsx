import React from 'react';
import ChartGrid from '@/components/Charts/charts/ChartGrid';
import EmissionsChart from '@/components/Charts/EmissionsChart';

const TestChartsPage = () => {
  return (
    <div className="p-6 space-y-8">
      <h1 className="text-2xl font-bold">Test Charts Page</h1>
      
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">Chart Grid</h2>
        <ChartGrid activeView="overview" selectedMetric="emissions" />
      </div>

      <div className="space-y-6">
        <h2 className="text-xl font-semibold">Emissions Chart</h2>
        <div className="bg-white rounded-lg shadow-md p-6">
          <EmissionsChart />
        </div>
      </div>
    </div>
  );
};

export default TestChartsPage;
