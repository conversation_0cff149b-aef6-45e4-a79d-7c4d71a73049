import { useState, useMemo } from "react";
import MetricCard from "@/components/Charts/MetricCard";
import MetricDetails from "@/components/Charts/MetricDetails";

const RiskAssessmentCharts = () => {
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});

  // Risk Assessment metrics data
  const overviewMetrics = [
    {
      id: "overdue-risk-mitigation-controls",
      title: "No. of overdue risk mitigation controls",
      value: "8",
      unit: "controls",
      target: 0,
      targetPercentage: 100,
      trend: [12, 11, 10, 9, 8, 7, 8, 9, 8, 7, 8, 8],
      isImproving: true
    },
    {
      id: "toolbox-talks-field-issues",
      title: "Percentage of tool box talks with field-reported issues",
      value: "15",
      unit: "talks (15%)",
      target: 0,
      targetPercentage: 15,
      trend: [18, 17, 16, 15, 14, 15, 16, 15, 14, 15, 16, 15],
      isImproving: true
    },
    {
      id: "unsafe-observations-permit-work",
      title: "No. of Unsafe Observations / Safety Incidents reported in work activities being performed under permit to work",
      value: "3",
      unit: "incidents",
      target: 0,
      targetPercentage: 100,
      trend: [6, 5, 4, 3, 2, 3, 4, 3, 2, 3, 4, 3],
      isImproving: true
    },
    {
      id: "permit-works-revoked",
      title: "Percentage of applied Permit to Works that were revoked",
      value: "2",
      unit: "permits (2%)",
      target: 0,
      targetPercentage: 2,
      trend: [5, 4, 3, 2, 1, 2, 3, 2, 1, 2, 3, 2],
      isImproving: true
    }
  ];

  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  return (
    <div className="space-y-6">
      {/* Headline Metrics */}
      <div className="grid grid-cols-4 gap-4">
        {overviewMetrics.map((metric) => (
          <MetricCard
            key={metric.id}
            {...metric}
            showDetails={openDetailsId === metric.id}
            onToggleDetails={() => handleToggleDetails(metric.id)}
            selectedPeriod={getSelectedPeriod(metric.id)}
            onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
          />
        ))}
      </div>

      {/* Details Section - Rendered below the grid when a card is selected */}
      {openDetailsId && (() => {
        const selectedMetric = overviewMetrics.find(m => m.id === openDetailsId);
        return selectedMetric ? (
          <MetricDetails
            title={selectedMetric.title}
            value={selectedMetric.value}
            unit={selectedMetric.unit}
            targetPercentage={selectedMetric.targetPercentage}
            trend={selectedMetric.trend}
            isImproving={selectedMetric.isImproving}
            selectedPeriod={getSelectedPeriod(openDetailsId)}
          />
        ) : null;
      })()}
    </div>
  );
};

export default RiskAssessmentCharts;
