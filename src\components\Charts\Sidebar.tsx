
import { useState } from "react";
import {
  Home,
  Settings,
  Users,
  Target,
  Building,
  Database,
  FileText,
  Shield,
  LogOut,
  ChevronDown,
  ChevronRight,
  FolderKanban
} from "lucide-react";

const Sidebar = () => {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    dataAcquisition: false,
    settings: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  return (
    <aside className="fixed left-0 top-20 w-64 bg-white/90 backdrop-blur-md shadow-xl border-r border-gray-200 h-[calc(100vh-5rem)] z-40">
      <div className="flex flex-col h-full">
        {/* Main Navigation - Scrollable */}
        <div className="flex-1 overflow-y-auto p-4">
          <nav className="space-y-2">
          {/* Main Navigation */}
          <div className="space-y-1">
            <button className="w-full flex items-center space-x-3 px-3 py-2 text-left text-blue-700 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg font-medium hover:from-blue-100 hover:to-indigo-100 transition-all duration-200 border border-blue-200">
              <Home className="h-5 w-5" />
              <span>Dashboard</span>
            </button>

            <button className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              <FolderKanban className="h-5 w-5" />
              <span>Program Management</span>
            </button>

            {/* Data Acquisition Section */}
            <div>
              <button
                onClick={() => toggleSection('dataAcquisition')}
                className="w-full flex items-center justify-between px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <Database className="h-5 w-5" />
                  <span>Data Acquisition</span>
                </div>
                {expandedSections.dataAcquisition ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </button>

              {expandedSections.dataAcquisition && (
                <div className="ml-8 mt-1 space-y-1">
                  <button className="w-full flex items-center space-x-2 px-3 py-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                    <span>Quantitative</span>
                  </button>
                  <button className="w-full flex items-center space-x-2 px-3 py-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                    <span>Qualitative</span>
                  </button>
                </div>
              )}
            </div>

            <button className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              <FileText className="h-5 w-5" />
              <span>Raw Data Repository</span>
            </button>

            <button className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              <Shield className="h-5 w-5" />
              <span>Data Assurance</span>
            </button>

            <button className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              <FileText className="h-5 w-5" />
              <span>Reports</span>
            </button>
          </div>
          </nav>
        </div>

        {/* Settings and Logout Section - Fixed at Bottom */}
        <div className="p-4 border-t border-gray-200 bg-white/95">
          <div className="space-y-2">
            <div>
              <button
                onClick={() => toggleSection('settings')}
                className="w-full flex items-center justify-between px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <Settings className="h-5 w-5" />
                  <span>Settings</span>
                </div>
                {expandedSections.settings ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </button>

              {expandedSections.settings && (
                <div className="ml-8 mt-1 space-y-1">
                  <button className="w-full flex items-center space-x-2 px-3 py-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                    <Users className="h-4 w-4" />
                    <span>User Management</span>
                  </button>
                  <button className="w-full flex items-center space-x-2 px-3 py-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                    <Target className="h-4 w-4" />
                    <span>Goal Setting</span>
                  </button>
                  <button className="w-full flex items-center space-x-2 px-3 py-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                    <Building className="h-4 w-4" />
                    <span>Value Chain Partners</span>
                  </button>
                </div>
              )}
            </div>

            <button className="w-full flex items-center space-x-3 px-3 py-2 text-left text-red-600 hover:bg-red-50 rounded-lg transition-colors">
              <LogOut className="h-5 w-5" />
              <span>Log Out</span>
            </button>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
