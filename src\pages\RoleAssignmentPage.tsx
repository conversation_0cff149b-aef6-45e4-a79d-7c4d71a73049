import { useState, useEffect, useCallback, useMemo } from "react";
import { useToast } from "@/components/ui/use-toast";
import PageHeader from "@/components/common/PageHeader";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";

import RoleAssignmentTable from "@/components/role-assignment/RoleAssignmentTable";
import DeleteConfirmationDialog from "@/components/role-assignment/DeleteConfirmationDialog";
import ResetRolesDialog from "@/components/role-assignment/ResetRolesDialog";
import AllFilterLocation from "@/components/observations/AllFilterLocation";

import { RoleAssignment } from "@/types/roleAssignment";
import { User } from "@/types/user";
import apiService from "@/services/apiService";
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import {
  EXTERNAL_USERS_URL,
  USERS_URL_WITH_ID,
  SERVICE_DETAILS,
  GET_INDIVIDUAL_USER_LOCATION_ROLE_URL,
  INDIVIDUAL_USER_LOCATION_ROLE_URL,
  USER_LOCATION_ROLE_URL,
  USERS_URL
} from '@/constants/index';
import { get } from "http";
import FilterLocation from "@/components/role-assignment/FilterLocations";

// Interface for API response
interface ApiUser {
  id: string;
  firstName: string;
  email: string;
  company: string;
  type: string;
  status: boolean;
  roles: string[];
  created: string;
  updated: string;
  customRoles?: any;
}

interface ServiceRole {
  id: string;
  name: string;
  maskName?: string;
  maskId?: string;
}

interface Service {
  id: string;
  name: string;
  maskName?: string;
  roles: ServiceRole[];
}

interface SelectedRoles {
  [serviceId: string]: string[];
}

interface IndividualSelectedRole {
  roles: string[];
  disabledRoles: string[];
}



const RoleAssignmentPage = () => {
  const { toast } = useToast();
  const [assignments, setAssignments] = useState<RoleAssignment[]>([]);
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [isPermissionsModalOpen, setIsPermissionsModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);
  const [selectedAssignmentId, setSelectedAssignmentId] = useState<string | null>(null);
  const [editingAssignment, setEditingAssignment] = useState<RoleAssignment | null>(null);

  // New state for API functionality
  const [externalUsers, setExternalUsers] = useState<ApiUser[]>([]);
  const [roles, setRoles] = useState<Service[]>([]);
  const [selectedRoles, setSelectedRoles] = useState<SelectedRoles>({});
  const [selectedUserId, setSelectedUserId] = useState({ id: "", email: "", name: "" });
  const [individualSelectedRole, setIndividualSelectedRole] = useState<IndividualSelectedRole>({ roles: [], disabledRoles: [] });
  const [isLoading, setIsLoading] = useState(false);

  // State for displaying user's assigned roles (matching test.js structure)
  const [allLocationRoles, setAllLocationRoles] = useState<any[]>([]);
  const [isLoadingUserRoles, setIsLoadingUserRoles] = useState(false);

  // Location data for name resolution (matching test.js)
  const [country, setCountry] = useState<{ id: string; name: string }[]>([]);
  const [locationTwo, setLocationTwo] = useState<{ id: string; name: string }[]>([]);
  const [locationThree, setLocationThree] = useState<{ id: string; name: string }[]>([]);
  const [locationFour, setLocationFour] = useState<{ id: string; name: string }[]>([]);

  // Dynamic location state - will be determined by configuration
  const [selectedLocations, setSelectedLocations] = useState<string[]>(['', '', '', '', '', '']);
  const [maxLocationLevels, setMaxLocationLevels] = useState<number>(4); // Default to 4, will be updated dynamically

  // Legacy state for backward compatibility
  const [selectedLocationOne, setSelectedLocationOne] = useState('');
  const [selectedLocationTwo, setSelectedLocationTwo] = useState('');
  const [selectedLocationThree, setSelectedLocationThree] = useState('');
  const [selectedLocationFour, setSelectedLocationFour] = useState('');
  const [selectedLocationFive, setSelectedLocationFive] = useState('');
  const [selectedLocationSix, setSelectedLocationSix] = useState('');

  const [roleAllocation, setRoleAllocation] = useState<any[]>([]);

  // Helper function to determine the number of active location levels
  const determineLocationLevels = (adminRoles: any[]): number => {
    if (!adminRoles || adminRoles.length === 0) return 4; // Default to 4 levels

    let maxLevel = 1;
    adminRoles.forEach(role => {
      role.location.forEach((loc: any) => {
        if (loc.locationFourId && loc.locationFourId !== 'tier4-all') maxLevel = Math.max(maxLevel, 4);
        else if (loc.locationThreeId && loc.locationThreeId !== 'tier3-all') maxLevel = Math.max(maxLevel, 3);
        else if (loc.locationTwoId && loc.locationTwoId !== 'tier2-all') maxLevel = Math.max(maxLevel, 2);
        else if (loc.locationOneId && loc.locationOneId !== 'tier1-all') maxLevel = Math.max(maxLevel, 1);

        // Check for tier-all values to determine maximum possible levels
        if (loc.locationFourId === 'tier4-all') maxLevel = Math.max(maxLevel, 4);
        else if (loc.locationThreeId === 'tier3-all') maxLevel = Math.max(maxLevel, 3);
        else if (loc.locationTwoId === 'tier2-all') maxLevel = Math.max(maxLevel, 2);
        else if (loc.locationOneId === 'tier1-all') maxLevel = Math.max(maxLevel, 1);
      });
    });

    return maxLevel;
  };

  // Fetch external users
  const fetchExternalUsers = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await apiService.get(USERS_URL);

      // Filter to show only External type users
      // const filteredUsers = (data || [])
      //   .filter((user: ApiUser) => user.type === "External")
      //   .sort((a: ApiUser, b: ApiUser) => a.firstName.toLowerCase().localeCompare(b.firstName.toLowerCase()));

      setExternalUsers(data || []);
    } catch (error) {
      console.error('Error fetching external users:', error);
      toast({
        title: "Error",
        description: "Failed to load external users. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Fetch services with roles (renamed to match test.js)
  const fetchServices = useCallback(async () => {
    try {
      const params = {
        "include": [
          { "relation": "roles" }
        ]
      };
      const response = await apiService.get(`${SERVICE_DETAILS}?filter=${encodeURIComponent(JSON.stringify(params))}`);
      setRoles(response || []);
    } catch (error) {
      console.error('Error fetching services:', error);
      toast({
        title: "Error",
        description: "Failed to load services. Please try again.",
        variant: "destructive"
      });
    }
  }, [toast]);

  // Fetch location data for name resolution (matching test.js)
  const fetchLocationData = useCallback(async () => {
    try {
      // Fetch location ones (countries)
      const countryData = await apiService.get('/location-ones');
      setCountry(countryData || []);

      // Note: In a real implementation, you might want to fetch all location data
      // For now, we'll fetch them as needed when displaying roles
    } catch (error) {
      console.error('Error fetching location data:', error);
    }
  }, []);

  const getLocationTwo = async () => {
    try {
      const response = await apiService.get('/location-twos');
      setLocationTwo(Array.isArray(response) ? response : []);
    } catch (error) {
      console.error('Failed to fetch location two:', error);
      setLocationTwo([]);
    }
  };

  const getLocationThree = async () => {
    try {
      const response = await apiService.get('/location-threes');
      setLocationThree(Array.isArray(response) ? response : []);
    } catch (error) {
      console.error('Failed to fetch location three:', error);
      setLocationThree([]);
    }
  };

  const getLocationFour = async () => {
    try {
      const response = await apiService.get('/location-fours');
      setLocationFour(Array.isArray(response) ? response : []);
    } catch (error) {
      console.error('Failed to fetch location four:', error);
      setLocationFour([]);
    }
  };

  const getRoleAllocation = async () => {
    try {
      const response = await apiService.get('/v2/users/me');
      const adminRoles = response.roles.filter(
        ({ maskName }) => maskName?.toLowerCase().endsWith('_admin')
      );

      // Determine the maximum location levels based on admin roles
      const maxLevels = determineLocationLevels(adminRoles);
      setMaxLocationLevels(maxLevels);

      // Example: print just the names
      console.log('Admin roles:', adminRoles);
      console.log('Max location levels:', maxLevels);
      setRoleAllocation(adminRoles || []);
    } catch (error) {
      console.error('Error fetching role allocation:', error);
    }
  };
  const myAdminRoleIds = useMemo(
    () => new Set(roleAllocation.map(r => r.id)),
    [roleAllocation]
  );

  // Function to check if selected location path matches admin role permissions
  const checkLocationMatch = useCallback((adminRole: any): boolean => {
    if (!adminRole.location || adminRole.location.length === 0) return false;

    return adminRole.location.some((loc: any) => {
      // Build the selected location path
      const selectedPath = {
        locationOneId: selectedLocationOne || null,
        locationTwoId: selectedLocationTwo || null,
        locationThreeId: selectedLocationThree || null,
        locationFourId: selectedLocationFour || null,
      };

      // Check if the selected path matches this admin role's location
      const matchesLocationOne = !loc.locationOneId ||
        loc.locationOneId === selectedPath.locationOneId ||
        loc.locationOneId === 'tier1-all';

      const matchesLocationTwo = !loc.locationTwoId ||
        loc.locationTwoId === selectedPath.locationTwoId ||
        loc.locationTwoId === 'tier2-all';

      const matchesLocationThree = !loc.locationThreeId ||
        loc.locationThreeId === selectedPath.locationThreeId ||
        loc.locationThreeId === 'tier3-all';

      const matchesLocationFour = !loc.locationFourId ||
        loc.locationFourId === selectedPath.locationFourId ||
        loc.locationFourId === 'tier4-all';

      // All levels must match for this location to be valid
      return matchesLocationOne && matchesLocationTwo && matchesLocationThree && matchesLocationFour;
    });
  }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour]);

  // Get matching admin roles based on selected location
  const getMatchingAdminRoles = useCallback(() => {
    if (!roleAllocation || roleAllocation.length === 0) return [];

    return roleAllocation.filter(adminRole => checkLocationMatch(adminRole));
  }, [roleAllocation, checkLocationMatch]);

  // Get roles that should be displayed based on matching admin roles
  const getFilteredRolesByService = useCallback((service: Service) => {
    const matchingAdminRoles = getMatchingAdminRoles();

    console.log('=== Role Filtering Debug ===');
    console.log('Selected Location Path:', {
      locationOne: selectedLocationOne,
      locationTwo: selectedLocationTwo,
      locationThree: selectedLocationThree,
      locationFour: selectedLocationFour
    });
    console.log('All Admin Roles:', roleAllocation);
    console.log('Matching Admin Roles:', matchingAdminRoles);
    console.log('Service:', service.name, service);

    if (matchingAdminRoles.length === 0) {
      console.log('No matching admin roles found for selected location');
      return [];
    }

    // For each matching admin role, check if it corresponds to this service
    const serviceRelatedRoles = matchingAdminRoles.filter(adminRole => {
      // Check if this admin role is related to the current service
      // Based on your sample data, admin roles have maskName like 'ra_admin', 'eptw_admin', etc.
      // We need to match these with service roles

      if (!adminRole.maskName) return false;

      // Extract the service prefix from admin role (e.g., 'ra' from 'ra_admin')
      const adminPrefix = adminRole.maskName.replace('_admin', '');

      // Check if any role in this service has a matching prefix
      return service.roles?.some(role =>
        role.maskName?.startsWith(adminPrefix) ||
        role.maskId?.startsWith(adminPrefix)
      ) || false;
    });

    console.log('Service Related Admin Roles:', serviceRelatedRoles);

    if (serviceRelatedRoles.length === 0) {
      console.log('No admin roles found for this service');
      return [];
    }

    // Get all non-admin role IDs for this service
    const allowedRoles = service.roles?.filter(role =>
      !role.maskName?.toLowerCase().endsWith('_admin') &&
      !role.maskId?.toLowerCase().endsWith('_admin')
    ) || [];

    console.log('Allowed Roles for Service:', allowedRoles);
    console.log('=== End Debug ===');

    return allowedRoles;
  }, [getMatchingAdminRoles, selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, roleAllocation]);
  // Load data on component mount
  useEffect(() => {
    fetchExternalUsers();
    fetchServices();
    fetchLocationData();
    getLocationTwo();
    getLocationThree();
    getLocationFour();
    getRoleAllocation();
  }, [fetchExternalUsers, fetchServices, fetchLocationData]);

  // Handle location filter - dynamic based on maxLocationLevels
  const handleFilter = (
    locationOneId: string,
    locationTwoId?: string,
    locationThreeId?: string,
    locationFourId?: string,
    locationFiveId?: string,
    locationSixId?: string
  ) => {
    // Update legacy state for backward compatibility
    setSelectedLocationOne(locationOneId);
    setSelectedLocationTwo(locationTwoId || '');
    setSelectedLocationThree(locationThreeId || '');
    setSelectedLocationFour(locationFourId || '');
    setSelectedLocationFive(locationFiveId || '');
    setSelectedLocationSix(locationSixId || '');

    // Update dynamic state
    const newSelectedLocations = [
      locationOneId,
      locationTwoId || '',
      locationThreeId || '',
      locationFourId || '',
      locationFiveId || '',
      locationSixId || ''
    ];
    setSelectedLocations(newSelectedLocations);
  };

  const getIndividualRoles = useCallback(async () => {
    try {
      // Build locations object dynamically based on maxLocationLevels
      const locations: any = {};
      const locationKeys = ['locationOne', 'locationTwo', 'locationThree', 'locationFour', 'locationFive', 'locationSix'];
      const locationValues = [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, selectedLocationFive, selectedLocationSix];

      for (let i = 0; i < Math.min(maxLocationLevels, locationKeys.length); i++) {
        if (locationValues[i]) {
          locations[locationKeys[i]] = locationValues[i];
        }
      }

      const response = await apiService.post(GET_INDIVIDUAL_USER_LOCATION_ROLE_URL, {
        userId: selectedUserId.id,
        locations
      });

      if (response && response.length > 0) {
        setIndividualSelectedRole(response[0]);
      } else {
        setIndividualSelectedRole({ roles: [], disabledRoles: [] });
      }
    } catch (error) {
      console.error('Error fetching individual roles:', error);
      setIndividualSelectedRole({ roles: [], disabledRoles: [] });
    }
  }, [selectedUserId.id, selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, selectedLocationFive, selectedLocationSix, maxLocationLevels]);

  // Helper function to check if we've reached the deepest available location level
  const hasReachedDeepestLevel = useCallback(async (): Promise<boolean> => {
    // If no location is selected, return false
    if (!selectedLocationOne) return false;

    // If tier1-all is selected, we can show roles
    if (selectedLocationOne === 'tier1-all') return true;

    // Check if level 2 exists for selected level 1
    if (selectedLocationOne && selectedLocationOne !== 'tier1-all') {
      try {
        const level2Data = await apiService.get(`/location-ones/${selectedLocationOne}/location-twos`);

        // If no level 2 data, we're at the deepest level (level 1)
        if (!level2Data || level2Data.length === 0) return true;

        // If level 2 exists but none selected, we're not at deepest level yet
        if (!selectedLocationTwo) return false;

        // If tier2-all is selected, we can show roles
        if (selectedLocationTwo === 'tier2-all') return true;

        // Check if level 3 exists for selected level 2
        if (selectedLocationTwo && selectedLocationTwo !== 'tier2-all') {
          const level3Data = await apiService.get(`/location-twos/${selectedLocationTwo}/location-threes`);

          // If no level 3 data, we're at the deepest level (level 2)
          if (!level3Data || level3Data.length === 0) return true;

          // If level 3 exists but none selected, we're not at deepest level yet
          if (!selectedLocationThree) return false;

          // If tier3-all is selected, we can show roles
          if (selectedLocationThree === 'tier3-all') return true;

          // Check if level 4 exists for selected level 3
          if (selectedLocationThree && selectedLocationThree !== 'tier3-all') {
            const level4Data = await apiService.get(`/location-threes/${selectedLocationThree}/location-fours`);

            // If no level 4 data, we're at the deepest level (level 3)
            if (!level4Data || level4Data.length === 0) return true;

            // If level 4 exists but none selected, we're not at deepest level yet
            if (!selectedLocationFour) return false;

            // If tier4-all is selected or specific level 4 is selected, we can show roles
            if (selectedLocationFour === 'tier4-all' || selectedLocationFour) return true;
          }
        }
      } catch (error) {
        console.error('Error checking location hierarchy:', error);
        return false;
      }
    }

    return false;
  }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour]);

  // State to track if we're at the deepest level
  const [isAtDeepestLevel, setIsAtDeepestLevel] = useState(false);

  // Check deepest level whenever location selections change
  useEffect(() => {
    const checkDeepestLevel = async () => {
      const isDeepest = await hasReachedDeepestLevel();
      setIsAtDeepestLevel(isDeepest);
    };

    checkDeepestLevel();
  }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, hasReachedDeepestLevel]);

  // Get individual roles when we reach the deepest level
  useEffect(() => {
    if (isAtDeepestLevel && selectedUserId.id) {
      getIndividualRoles();
    }
  }, [isAtDeepestLevel, selectedUserId.id, getIndividualRoles]);

  // Function to get role name by ID (matching test.js)
  const getRoleNameById = useCallback((roleId: string): string => {
    for (const service of roles) {
      if (service.roles) {
        const role = service.roles.find(r => r.id === roleId);
        if (role) {
          return role.name;
        }
      }
    }
    return 'Unknown Role';
  }, [roles]);

  // Fetch user's assigned roles (matching test.js structure)
  const fetchUserAssignedRoles = useCallback(async (userId: string) => {
    if (!userId) {
      setAllLocationRoles([]);
      return;
    }

    try {
      setIsLoadingUserRoles(true);

      // Fetch user location roles (matching test.js API call)
      const locationRolesResponse = await apiService.post(GET_INDIVIDUAL_USER_LOCATION_ROLE_URL, {
        userId: userId,
        locations: {} // Get all location roles for this user
      });

      if (locationRolesResponse && Array.isArray(locationRolesResponse)) {
        setAllLocationRoles(locationRolesResponse);
      } else {
        setAllLocationRoles([]);
      }
    } catch (error) {
      console.error('Error fetching user assigned roles:', error);
      setAllLocationRoles([]);
      toast({
        title: "Error",
        description: "Failed to load user's assigned roles.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingUserRoles(false);
    }
  }, [toast]);

  const handleUserSelect = (userId: string) => {
    setSelectedUser(userId);
    fetchUserAssignedRoles(userId);
  };

  const handleAssignPermissions = async () => {
    if (!selectedUser) {
      toast({
        title: "No User Selected",
        description: "Please select a user to assign permissions.",
        variant: "destructive"
      });
      return;
    }

    // Find the selected user
    const user = externalUsers.find(u => u.id === selectedUser);
    if (!user) {
      toast({
        title: "User Not Found",
        description: "Selected user not found.",
        variant: "destructive"
      });
      return;
    }

    // Fetch user's existing roles
    try {
      const response = await apiService.get(USERS_URL_WITH_ID(selectedUser));

      if (response.customRoles) {
        setSelectedRoles(response.customRoles);
      } else {
        // Initialize with empty roles for each service
        const emptyRoles: SelectedRoles = {};
        roles.forEach(service => {
          emptyRoles[service.id] = [];
        });
        setSelectedRoles(emptyRoles);
      }

      setSelectedUserId({ id: user.id, email: user.email, name: user.firstName });
      setIsPermissionsModalOpen(true);
    } catch (error) {
      console.error('Error fetching user roles:', error);
      toast({
        title: "Error",
        description: "Failed to load user roles. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Handle role changes
  const handleRoleChange = (e: React.ChangeEvent<HTMLInputElement>, serviceId: string) => {
    const roleId = e.target.value;

    setIndividualSelectedRole((prevRoles) => {
      if (e.target.checked) {
        // Add the role to the selected roles
        return { ...prevRoles, roles: [...prevRoles.roles, roleId] };
      } else {
        // Remove the role from the selected roles
        return { ...prevRoles, roles: prevRoles.roles.filter((id) => id !== roleId) };
      }
    });

    setSelectedRoles((prevRoles) => {
      // Initialize the category roles array if it doesn't exist
      const categoryRoles = prevRoles[serviceId] || [];

      console.log(serviceId, prevRoles, categoryRoles, 'check');

      if (e.target.checked) {
        // Add the role to the selected roles if it's not already there
        if (!categoryRoles.includes(roleId)) {
          return {
            ...prevRoles,
            [serviceId]: [...categoryRoles, roleId],
          };
        }
      } else {
        // Remove the role from the selected roles
        return {
          ...prevRoles,
          [serviceId]: categoryRoles.filter((id) => id !== roleId),
        };
      }
      return prevRoles;
    });
  };

  // Handle assignment submission
  const handleAssignSubmit = async () => {
    const id = selectedUserId.id;
    let flag = false;

    try {
      // Build locations object dynamically based on maxLocationLevels
      const locations: any = {};
      const locationKeys = ['locationOne', 'locationTwo', 'locationThree', 'locationFour', 'locationFive', 'locationSix'];
      const locationValues = [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, selectedLocationFive, selectedLocationSix];

      for (let i = 0; i < Math.min(maxLocationLevels, locationKeys.length); i++) {
        if (locationValues[i]) {
          locations[locationKeys[i]] = locationValues[i];
        }
      }

      const response = await apiService.post(USER_LOCATION_ROLE_URL, {
        userId: id,
        roles: individualSelectedRole.roles,
        locations
      });

      if (response) {
        flag = true;
      }

      // const response2 = await apiService.patch(USERS_URL_WITH_ID(id), { customRoles: selectedRoles });

      if ( flag) {
        toast({
          title: "Roles Assigned",
          description: `Roles have been assigned to ${selectedUserId.name} successfully.`,
          variant: "default"
        });
        handleAssignClose();
      }
    } catch (error) {
      console.error('Error assigning roles:', error);
      toast({
        title: "Error",
        description: "Failed to assign roles. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleAssignClose = () => {
    setSelectedRoles({});
    setSelectedUserId({ id: "", email: "", name: "" });
    setIsPermissionsModalOpen(false);
    setIndividualSelectedRole({ roles: [], disabledRoles: [] });
    setSelectedLocationOne('');
    setSelectedLocationTwo('');
    setSelectedLocationThree('');
    setSelectedLocationFour('');
    setSelectedLocationFive('');
    setSelectedLocationSix('');
  };

  const handleEdit = (assignment: RoleAssignment) => {
    setEditingAssignment(assignment);
    setSelectedUser(assignment.userId);
    setIsPermissionsModalOpen(true);
  };

  const handleDelete = (assignmentId: string) => {
    setSelectedAssignmentId(assignmentId);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (selectedAssignmentId) {
      setAssignments(prev => prev.filter(a => a.id !== selectedAssignmentId));
      toast({
        title: "Assignment Deleted",
        description: "The role assignment has been deleted successfully.",
        variant: "default"
      });
      setIsDeleteDialogOpen(false);
      setSelectedAssignmentId(null);
    }
  };

  const handleReset = (assignmentId: string) => {
    setSelectedAssignmentId(assignmentId);
    setIsResetDialogOpen(true);
  };

  const confirmReset = () => {
    if (selectedAssignmentId) {
      setAssignments(prev =>
        prev.map(a => a.id === selectedAssignmentId ? { ...a, roles: [] } : a)
      );
      toast({
        title: "Roles Reset",
        description: "All roles have been removed from this assignment.",
        variant: "default"
      });
      setIsResetDialogOpen(false);
      setSelectedAssignmentId(null);
    }
  };

  return (
    <>
      <PageHeader
        title="Project Role Assignment"
        description="Configure user roles and specific permissions for different modules of the system"
      />

      <Card className="mb-6 animate-slide-up">
        <CardContent className="p-6">
          <h3 className="text-lg font-medium text-[#0A5A8F] mb-4">User Selection</h3>

          <div>
            <p className="text-sm font-medium mb-2">Select User</p>
            <div className="flex justify-between items-center">
              <div className="w-full max-w-md">
                <Select
                  value={selectedUser}
                  onValueChange={handleUserSelect}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select User" />
                  </SelectTrigger>
                  <SelectContent>
                    {externalUsers.map(user => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.firstName} ({user.email})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <Button
                variant="gradient"
                className="ml-4 shadow-sm"
                onClick={handleAssignPermissions}
              >
                Assign Permissions
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User's Assigned Roles Table (matching test.js functionality) */}
      {selectedUser && (
        <Card className="animate-slide-up" style={{ animationDelay: "50ms" }}>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">List of Assigned Roles</h3>
              <hr className="border-gray-300" />

              {isLoadingUserRoles ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-[#0A5A8F]" />
                  <span className="ml-2 text-gray-600">Loading assigned roles...</span>
                </div>
              ) : allLocationRoles.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium">Location</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium">Assigned Roles</th>
                        <th className="border border-gray-300 px-4 py-2 text-left font-medium">Date Assigned</th>
                      </tr>
                    </thead>
                    <tbody>
                      {allLocationRoles.map((locationRole: any, index: number) => {
                        // Initialize an array to hold the names for each level (matching test.js)
                        let locationNames: string[] = [];
                        const roleNames = locationRole.roles.map((roleId: string) => getRoleNameById(roleId));

                        // Function to get name by ID or handle special 'all' cases (matching test.js exactly)
                        const getLocationNameLocal = (id: string, locationArray: { id: string; name: string }[], allText: string): string => {
                          if (id === "") return ""; // Return empty if ID is not set
                          if (id.endsWith("-all")) return allText; // Handle 'all' cases
                          const location = locationArray.find((location: { id: string; name: string }) => location.id === id);
                          return location ? location.name : 'Unknown';
                        };

                        // Get names for each level dynamically based on maxLocationLevels
                        const locationIds = [
                          locationRole.locationOneId || "",
                          locationRole.locationTwoId || "",
                          locationRole.locationThreeId || "",
                          locationRole.locationFourId || "",
                          locationRole.locationFiveId || "",
                          locationRole.locationSixId || ""
                        ];
                        const locationArrays = [country, locationTwo, locationThree, locationFour, [], []]; // Add empty arrays for levels 5 and 6 if needed

                        for (let i = 0; i < Math.min(maxLocationLevels, locationIds.length); i++) {
                          if (locationIds[i]) {
                            locationNames.push(getLocationNameLocal(locationIds[i], locationArrays[i], 'All'));
                          }
                        }

                        // Filter out empty or unknown locations before joining (matching test.js)
                        locationNames = locationNames.filter(name => name && name !== 'Unknown');

                        return (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="border border-gray-300 px-4 py-2">
                              <span className="font-medium text-sm">
                                {locationNames.join(' > ') || 'Global'}
                              </span>
                            </td>
                            <td className="border border-gray-300 px-4 py-2">
                              <div className="space-y-1">
                                {roleNames.map((name: string, roleIndex: number) => (
                                  <span key={roleIndex} className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1 mb-1">
                                    {name}
                                  </span>
                                ))}
                              </div>
                            </td>
                            <td className="border border-gray-300 px-4 py-2">
                              <span className="text-sm text-gray-600">
                                {locationRole.created ? new Date(locationRole.created).toLocaleDateString() : 'N/A'}
                              </span>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 border rounded-md bg-gray-50">
                  <p className="text-gray-500 text-sm">No assigned roles found for this user</p>
                  <p className="text-gray-400 text-xs mt-1">Click "Assign Permissions" to add roles</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* <Card className="animate-slide-up" style={{ animationDelay: "100ms" }}>
        <CardContent className="p-6">
          <h3 className="text-lg font-medium text-[#0A5A8F] mb-4">Current Assignments</h3>

          <RoleAssignmentTable
            assignments={assignments}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onReset={handleReset}
          />
        </CardContent>
      </Card> */}

      {/* Assign Permissions Modal */}
      <Dialog open={isPermissionsModalOpen} onOpenChange={setIsPermissionsModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Assign Permissions to {selectedUserId.name} ({selectedUserId.email})</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Location Selection */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Location Selection</h3>
              <FilterLocation
                handleFilter={handleFilter}
                disableAll={true}
                maxLevels={maxLocationLevels}
                adminRoles={roleAllocation}
              />
              {!isAtDeepestLevel && (selectedLocationOne || selectedLocationTwo || selectedLocationThree) && (
                <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                  <p className="text-sm text-blue-700">
                    <span className="font-medium">Note:</span> Please select the most specific location available to assign roles.
                    Continue selecting deeper location levels if available.
                  </p>
                </div>
              )}
              {isAtDeepestLevel && (selectedLocationOne || selectedLocationTwo || selectedLocationThree || selectedLocationFour) && (
                <div className="bg-green-50 border border-green-200 rounded-md p-3">
                  <p className="text-sm text-green-700">
                    <span className="font-medium">✓ Ready:</span> You've reached the most specific location level.
                    You can now assign roles below.
                  </p>
                </div>
              )}
            </div>

            {/* Role Selection */}
            {(isAtDeepestLevel && individualSelectedRole.roles) && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium">Role Assignment</h3>
                {getMatchingAdminRoles().length > 0 ? (
                  roles
                    .map((service: Service) => {
                      const filteredRoles = getFilteredRolesByService(service);
                      if (filteredRoles.length === 0) return null;

                      return (
                        <div key={service.id} className="space-y-4">
                          <h4 className="text-md font-medium text-[#0A5A8F]">{service.name}</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {filteredRoles.map((role: ServiceRole) => {
                              const isDisabled = individualSelectedRole.disabledRoles.includes(role.id);
                              const isChecked = individualSelectedRole.roles.includes(role.id) || isDisabled;
                              return (
                                <div key={role.id} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={role.id}
                                    checked={isChecked}
                                    disabled={isDisabled}
                                    onCheckedChange={(checked) => {
                                      const event = {
                                        target: {
                                          value: role.id,
                                          checked: checked as boolean
                                        }
                                      } as React.ChangeEvent<HTMLInputElement>;
                                      handleRoleChange(event, service.maskName || service.id);
                                    }}
                                  />
                                  <Label
                                    htmlFor={role.id}
                                    className={`text-sm ${isDisabled ? 'opacity-50' : ''}`}
                                  >
                                    {role.name}
                                  </Label>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      );
                    })
                    .filter(Boolean)
                ) : (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <p className="text-sm text-yellow-700">
                      <span className="font-medium">No matching permissions:</span> The selected location path does not match any of your admin role permissions. Please select a location where you have administrative access.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>

          <DialogFooter className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleAssignClose}>
              Close
            </Button>
            {selectedUserId.id && (
              <Button onClick={handleAssignSubmit}>
                Assign
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <DeleteConfirmationDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={confirmDelete}
        title="Delete Assignment"
        description="Are you sure you want to delete this role assignment? This action cannot be undone."
      />

      <ResetRolesDialog
        open={isResetDialogOpen}
        onOpenChange={setIsResetDialogOpen}
        onConfirm={confirmReset}
      />
    </>
  );
};

export default RoleAssignmentPage;
