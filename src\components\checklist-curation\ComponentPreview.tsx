import React from "react";
import { DroppedItem, ContentComponent } from "@/types/draggable";
import {
  Image,
  Video,
  Youtube,
  Link,
  FileText,
  Mic,
  File,
  Code,
  Boxes,
  ListChecks,
  TextQuote,
  Star,
  Calendar,
  Clock,
  Timer,
  Phone,
  Hash,
  PenTool,
  CheckSquare,
  ChevronDown,
  CircleCheck,
  CheckCircle,
  ClipboardList,
} from "lucide-react";

interface ComponentPreviewProps {
  item: DroppedItem;
}

const ComponentPreview: React.FC<ComponentPreviewProps> = ({ item }) => {
  const getComponentIcon = () => {
    const iconProps = { className: "h-4 w-4" };

    switch (item.type) {
      case "image":
        return <Image {...iconProps} />;
      case "video":
        return <Video {...iconProps} />;
      case "youtube":
        return <Youtube {...iconProps} />;
      case "weblink":
        return <Link {...iconProps} />;
      case "text":
        return <FileText {...iconProps} />;
      case "audio":
        return <Mic {...iconProps} />;
      case "attachment":
        return <File {...iconProps} />;
      case "embed":
        return <Code {...iconProps} />;
      case "scorm":
        return <Boxes {...iconProps} />;
      case "webgl":
        return <Boxes {...iconProps} />;
      case "mcq":
        return <ListChecks {...iconProps} />;
      case "textbox":
        return <TextQuote {...iconProps} />;
      case "option":
        return <CircleCheck {...iconProps} />;
      case "dropdown":
        return <ChevronDown {...iconProps} />;
      case "sign":
        return <PenTool {...iconProps} />;
      case "checkbox":
        return <CheckSquare {...iconProps} />;
      case "star":
        return <Star {...iconProps} />;
      case "date":
        return <Calendar {...iconProps} />;
      case "time":
        return <Clock {...iconProps} />;
      case "duration":
        return <Timer {...iconProps} />;
      case "phone":
        return <Phone {...iconProps} />;
      case "alphanumeric":
        return <Hash {...iconProps} />;
      case "checkpoint":
        return <CheckCircle {...iconProps} />;
      case "checkpoint-group":
        return <ClipboardList {...iconProps} />;
      default:
        return <File {...iconProps} />;
    }
  };

  const getComponentLabel = () => {
    switch (item.type) {
      case "image":
        return "Image";
      case "video":
        return "Video";
      case "youtube":
        return "YouTube";
      case "weblink":
        return "Web Link";
      case "text":
        return "Text";
      case "audio":
        return "Audio";
      case "attachment":
        return "Attachment";
      case "embed":
        return "Embed";
      case "scorm":
        return "SCORM";
      case "webgl":
        return "WebGL";
      case "mcq":
        return "Multiple Choice";
      case "textbox":
        return "Text Input";
      case "option":
        return "Option";
      case "dropdown":
        return "Dropdown";
      case "sign":
        return "Signature";
      case "checkbox":
        return "Checkbox";
      case "star":
        return "Star Rating";
      case "date":
        return "Date";
      case "time":
        return "Time";
      case "duration":
        return "Duration";
      case "phone":
        return "Phone Number";
      case "alphanumeric":
        return "Alphanumeric";
      case "checkpoint":
        return "Checkpoint";
      case "checkpoint-group":
        return "Checkpoint Group";
      default:
        return "Component";
    }
  };

  const renderPreview = () => {
    const data = item.data;

    switch (item.type) {
      case "image":
        return data.url ? (
          <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center overflow-hidden">
            <div className="text-xs text-muted-foreground">Image: {data.title || "Untitled"}</div>
          </div>
        ) : (
          <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
            <Image className="h-8 w-8 text-muted-foreground opacity-50" />
          </div>
        );

      case "text":
        return (
          <div className="text-sm">
            {data.content ? (
              <div dangerouslySetInnerHTML={{ __html: data.content }} />
            ) : (
              <p className="text-muted-foreground">Text content will appear here</p>
            )}
          </div>
        );

      case "mcq":
        return (
          <div>
            <p className="text-sm font-medium mb-2">
              {data.question || "Question text"}
              {data.required && <span className="text-red-500 ml-1">*</span>}
            </p>
            <div className="space-y-2">
              {(data.options || [{ text: "Option 1" }, { text: "Option 2" }]).map((option, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className={`w-4 h-4 border border-primary flex-shrink-0 ${
                    data.allowMultiple ? 'rounded-sm' : 'rounded-full'
                  }`}></div>
                  <span className="text-sm">{typeof option === 'string' ? option : option.text}</span>
                </div>
              ))}
            </div>
            <div className="text-xs text-muted-foreground mt-2">
              {data.allowMultiple ? 'Multiple selections allowed' : 'Single selection only'}
            </div>
          </div>
        );

      case "textbox":
        return (
          <div>
            <p className="text-sm font-medium mb-2">{(data as any).label || "Text input"}</p>
            <div className="border rounded-md p-2 text-sm text-muted-foreground">
              {(data as any).placeholder || "Enter text here..."}
            </div>
          </div>
        );

      case "checkpoint-group":
        const checkpointGroupData = data as any;
        return (
          <div className="space-y-3">
            {checkpointGroupData.title && (
              <div>
                <h4 className="font-semibold text-sm">{checkpointGroupData.title}</h4>
                {checkpointGroupData.description && (
                  <p className="text-xs text-muted-foreground mt-1">{checkpointGroupData.description}</p>
                )}
              </div>
            )}
            {checkpointGroupData.checkpoints && checkpointGroupData.checkpoints.length > 0 ? (
              <div className="space-y-3">
                {checkpointGroupData.checkpoints.map((checkpoint: any) => (
                  <div key={checkpoint.id} className="space-y-2">
                    <p className="text-sm font-medium">{checkpoint.text}</p>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 border border-primary rounded-full flex-shrink-0"></div>
                        <span className="text-xs">Yes</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 border border-primary rounded-full flex-shrink-0"></div>
                        <span className="text-xs">No</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 border border-primary rounded-full flex-shrink-0"></div>
                        <span className="text-xs">N/A</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">
                <ClipboardList className="h-6 w-6 text-muted-foreground mx-auto mb-2" />
                <span className="text-xs text-muted-foreground">
                  {checkpointGroupData.title ? 'No questions added' : 'No checkpoint group configured'}
                </span>
              </div>
            )}
          </div>
        );

      // Add more cases for other component types as needed

      default:
        return (
          <div className="text-sm text-muted-foreground">
            {getComponentLabel()} preview
          </div>
        );
    }
  };

  // Determine if this is a communicate or feedback component
  const isCommunicateComponent = !item.type.startsWith('feedback-') &&
    !['mcq', 'textbox', 'option', 'dropdown', 'sign', 'checkbox', 'star', 'date', 'time', 'duration', 'phone', 'alphanumeric'].includes(item.type);

  return (
    <div className="border rounded-md p-3 bg-white dark:bg-slate-800 shadow-sm">
      <div className="flex items-center gap-2 mb-2">
        <div className="text-primary p-1 bg-primary/10 rounded-md">
          {getComponentIcon()}
        </div>
        <div className="text-sm font-medium">{getComponentLabel()}</div>
      </div>
      <div className="mt-2">
        {renderPreview()}
      </div>
    </div>
  );
};

export default ComponentPreview;
