import React, { useEffect, useRef, useState } from 'react';
import API from '../services/API';
import { ASSIGNED_ACTION_URL, AIR_WITH_ID_URL, STATIC_URL, FILE_URL, RA_TEAM_MEMBER_SIGN, GET_RISK_WITH_ID_URL } from '../constants';

import moment from 'moment';
import { ThemeProvider, createTheme } from "@mui/material";
import MaterialTable from "material-table";
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";

import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap'
import Signature<PERSON>an<PERSON> from 'react-signature-canvas'
import $ from "jquery";
// import S3 from "react-aws-s3";
import { useSelector } from 'react-redux';
import Swal from 'sweetalert2';
import EditRoutine from './EditRoutine';
Buffer.from("anything", "base64");
window.Buffer = window.Buffer || require("buffer").Buffer;
window.jQuery = $;
// @ts-ignore
window.$ = $;
// const config = {
//     bucketName: "sagt",
//     region: "ap-southeast-1",
//     accessKeyId: process.env.REACT_APP_AWS_ACCESS_KEY,
//     secretAccessKey: process.env.REACT_APP_AWS_SECRET_KEY,
// };

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})
const Action = ({ data, onFilterUpdate }) => {
    const user = useSelector((state) => state.login.user)
    console.log(user)
    const signRef = useRef()
    const [actions, setActions] = useState([]);
    const [incidentData, setIncidentData] = useState({});
    const [modalState, setModalState] = useState({ type: null, isOpen: false, actionId: null });
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
        actionType: { value: null, matchMode: FilterMatchMode.IN },
        createdDate: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.DATE_IS }] },
        'submittedBy.firstName': { value: null, matchMode: FilterMatchMode.IN },
    });
    const [startDate, setStartDate] = useState(null)
    const [endDate, setEndDate] = useState(null)
    const [Search, setSearch] = useState([])
    const [maskId, setMaskId] = useState([])
    const [names, setNames] = useState([])
    const [dates, setDates] = useState([])
    const [showModal, setShowModal] = useState(false)
    const [showItem, setShowItem] = useState([])

    const [modalType, setModalType] = useState('')
    const [riskData, setRiskData] = useState([])
    const [domain, setDomain] = useState('')

    useEffect(() => {
        // getActions();
        getActionRa()
    }, [data])

    const getActionRa = async () => {

        setActions(data)

        const teamLeaderNames = data
            .map(item => ({
                name: item.submittedBy?.firstName,
                value: item.submittedBy?.firstName // or use item.teamLeader.id if you prefer the ID
            }))
            .filter((obj, index, self) =>
                index === self.findIndex((t) => t.name === obj.name)
            );


        setNames(teamLeaderNames)


    }

    // const getActions = async () => {
    //     const response = await API.get(ACTION_URL);
    //     if (response.status === 200) {

    //         if (props.id) {
    //             const actions = response.data

    //                 .filter(i => i.application === props.application && (i.objectId === props.id) && i.status !== 'completed')
    //                 .reverse()
    //             setActions(actions);
    //             setSearch(actions)

    //             const modifiedArray = actions.map(item => ({
    //                 name: item.applicationDetails.maskId ? item.applicationDetails.maskId : item.applicationDetails.docId,
    //                 value: item.applicationDetails.maskId ? item.applicationDetails.maskId : item.applicationDetails.docId // This adds the 
    //             }));
    //             let pp = modifiedArray.filter((ele, ind) => ind === modifiedArray.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
    //             setMaskId(pp)
    //             const name = actions.map(item => ({

    //                 name: item.actionSubmittedBy.firstName,
    //                 value: item.actionSubmittedBy.firstName // This adds the 
    //             }));
    //             let pp1 = name.filter((ele, ind) => ind === name.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
    //             setNames(pp1)
    //             const date = actions.map(item => ({

    //                 name: item.createdDate,
    //                 value: item.createdDate  // This adds the 
    //             }));
    //             let pp2 = date.filter((ele, ind) => ind === date.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
    //             setDates(pp2)
    //         } else {
    //             const actions = response.data.filter(i => i.application === 'RA' && i.status !== 'completed').reverse()

    //             setActions(actions);
    //             setSearch(actions);


    //             const date = actions.map(item => ({

    //                 name: item.created,
    //                 value: item.created  // This adds the 
    //             }));
    //             let pp2 = date.filter((ele, ind) => ind === date.findIndex(elem => elem.value === ele.value && elem.name === ele.name))
    //             setDates(pp2)
    //         }

    //     }
    // }

    const getSubmittedBy = (action) => {
        return action.actionSubmittedBy?.firstName || '';
    };


    const openActionCard = async (action) => {

        const uriString = {

            include: [
                { relation: "department" },
                { relation: "teamLeader" },
                { relation: "workActivity" },
                {
                    relation: "raTeamMembers",
                    scope: {
                        include: [{ relation: "user" }]
                    }
                }
            ]
        };

        const url = `${GET_RISK_WITH_ID_URL(action.applicationId)}?filter=${encodeURIComponent(
            JSON.stringify(uriString)
        )}`;
        const response = await API.get(url);
        if (response.status === 200) {


            setModalType(response.data.type === 'Routine' ? 'routine' : response.data.type === 'Non Routine' ? 'nonroutine' : 'hazard'); // 'routine' or 'nonroutine'
            setRiskData(response.data);  // Clear any previous data // Open the modal
            setDomain('view')

        }

        // await getReportIncident(action.objectId, action.id);
        // setModalState({ type: action.actionType, isOpen: true, actionId: action.id });
        console.log(action + 'test')
        setShowItem(action);
        setShowModal(true)
    };







    const onDateSearch = () => {
        const [from, to] = [startDate, endDate];
        if (from === null && to === null) return true;
        if (from !== null && to === null) return true;
        if (from === null && to !== null) return true;
        const start = moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month');
        const end = moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month');

        //  console.log(start,end)
        const searchData = Search.filter(item => isBetweenDateRange(item.createdDate, start, end))

        setActions(searchData)

        // return isBetweenDateRange(value, moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month'), moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month'))
    }
    const isBetweenDateRange = (dateString, date1, date2) => {
        // Parse the date strings using Moment.js
        const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

        // Check if the parsed date is between date1 and date2
        return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
    }


    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-end'>
                {/* <div className="">
                    <span className='me-3'>Month Filter :</span>
                    <Calendar view='month' className="w-full me-2" value={startDate} placeholder='From' onChange={(e) => setStartDate(e.value)} dateFormat="mm/yy" showIcon />
                    <Calendar view='month' className="w-full  me-3" value={endDate} placeholder="To" onChange={(e) => setEndDate(e.value)} dateFormat="mm/yy" showIcon />

                    <Button icon="pi pi-search " className='me-3' rounded text raised severity="success" aria-label="Search" onClick={() => onDateSearch()} />
                    <Button icon="pi pi-times " rounded text raised severity="danger" aria-label="Cancel" onClick={() => { setActions(Search); setStartDate(null); setEndDate(null) }} />

                </div> */}
                {/* <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText type="search" value={value || ''} onChange={(e) => onGlobalFilterChange(e)} placeholder="Global Search" />
                </span> */}
            </div>
        );
    };

    const header = renderHeader();
    const onGlobalFilterChange = (event) => {
        const value = event.target.value;
        let _filters = { ...filters };

        _filters['global'].value = value;

        setFilters(_filters);
    };
    const descBodyTemplate = (row) => {
        return 'Confirm my participation in this Risk Assessment as a team member';
    }
    const nameBodyTemplate = (row) => {
        return String(row.actionSubmittedBy.firstName);
    }
    const idBodyTemplate = (row) => {
        return <div className='maskid' onClick={() => openActionCard(row)}><span className='pending'></span>{row.maskId}</div>;
    }
    const submitBodyTemplate = (row) => {
        return getSubmittedBy(row);
    }
    const maskIdFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">ID Picker</div>
                <MultiSelect value={options.value} options={maskId} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const descFilterTemplate = (options) => {
        return (
            <div className='d-flex justify-content-end'>
                <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText type="search" value={options.value} onChange={(e) => options.filterCallback(e.value)} placeholder="Search" />
                </span>
            </div>
        )
    }
    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };
    const nameFilterTemplate = (options) => {
        console.log(options)
        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Name</div>
                <MultiSelect value={options.value} options={names} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const dateFilterTemplate = (options) => {

        // return (

        //     <React.Fragment>
        //         <div className="mb-3 font-bold">Date</div>
        //         <MultiSelect value={options.value} options={dates} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
        //     </React.Fragment>
        // );
        return <Calendar value={options.value} onChange={(e) => options.filterCallback(e.value, options.index)} dateFormat="mm/dd/yy" placeholder="mm/dd/yyyy" mask="99/99/9999" />;
    }

    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }

        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
    };

    const onConfirm = async () => {


        const filename = new Date().getTime() + "captin_sign.png";

        if (!signRef.current.isEmpty()) {

            const formData1 = new FormData();
            formData1.append('file', dataURItoFile(signRef.current.getTrimmedCanvas().toDataURL("image/png"), filename));

            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    }
                });

                if (response && response.status === 200) {

                    if (!signRef.current.isEmpty()) {

                        const response1 = await API.patch(RA_TEAM_MEMBER_SIGN(showItem.id), {
                            signature: response.data.files[0].originalname,
                            signatureDate: new Date()

                        })
                        if (response1.status === 204) {
                            customSwal2.fire(
                                'Risk Assessment Updated!',
                                '',
                                'success'
                            )
                            // getActions();
                            getActionRa();
                            setShowModal(false)

                        }
                    }
                }
            } catch (error) {
                console.error("File upload error: ", error);
            }

        }
    }

    const createdBodyTemplate = (row) => {
        return moment(row.created).format('DD-MM-YYYY')
    }

    const handleFilterChange = (filteredData) => {


        // Update the count of filtered data in the parent component
        if (onFilterUpdate) {
            onFilterUpdate(filteredData.length);
        }
    };
    return (

        <>


            <DataTable value={actions} paginator rows={10} onValueChange={handleFilterChange} globalFilterFields={["maskId", "createdDate", "actionSubmittedBy.firstName"]} header={header} filters={filters} onFilter={(e) => { if (e.filters['actionSubmittedBy.firstName'] !== undefined) { e.filters['actionSubmittedBy.firstName'].matchMode = 'in'; setFilters(e.filters) } else { setFilters(e.filters) } }}
                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }}>

                <Column field="maskId" header="ID" body={idBodyTemplate} sortable style={{ width: '25%' }} ></Column>

                <Column field='actionType' header="Description" body={descBodyTemplate} style={{ width: '25%' }}></Column>

                <Column field="created" header="Submitted On" body={createdBodyTemplate} sortable style={{ width: '25%' }}></Column>

                <Column field="submittedBy.firstName" header="Submitted By" filter showFilterMatchModes={false} filterElement={nameFilterTemplate} filterPlaceholder="Search" style={{ width: '25%' }}></Column>

            </DataTable>
            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>



                    <div className="w-100 d-flex justify-content-between align-items-center">
                        <h2 className="m-0"> Please Confirm</h2>
                        <div className="text-center mt-0">

                        </div>
                    </div>

                </Modal.Header>

                <Modal.Body>
                    <EditRoutine domain={domain} type={modalType} data={riskData} />
                    <div className="row mt-4">
                        <div className={`animate-col-md-12 text-center`}>
                            <div className='mb-4'>I confirm my participation in this Risk Assessment as a team member. The outcome reflects our shared professional judgment to the best of our abilities through consensus.</div>
                            <SignatureCanvas
                                penColor="#1F3BB3"
                                canvasProps={{
                                    width: 350,
                                    height: 100,
                                    className: "sigCanvas",
                                    style: {
                                        boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                                    },
                                }}
                                ref={signRef}


                            />  <i className="fa fa-undo undo" onClick={() => signRef.current.clear()}></i>
                        </div>
                    </div>
                </Modal.Body>
                <Modal.Footer className="flex-wrap">



                    <Button onClick={onConfirm}>Done</Button>
                    <Button variant="light" onClick={() => { setShowModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>
            {/* {renderModal()} */}
        </>
    );


}

export default Action;
