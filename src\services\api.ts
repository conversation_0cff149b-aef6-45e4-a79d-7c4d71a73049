import { LoginConfig, UserDetails } from '../store/slices/authSlice';
import apiService from './apiService';
import { fetchWithAuth } from '../utils/fetchWithAuth';

const API_BASE_URL = 'https://client-api.acuizen.com';

export const ASSIGNED_ACTION_URL = (id: string) => {
  return `${API_BASE_URL}/my-assigned-actions/${id}`;
};

export const fetchLoginConfig = async (): Promise<LoginConfig> => {
  try {
    const response = await fetch(`${API_BASE_URL}/login-configs`);

    if (!response.ok) {
      throw new Error(`Failed to fetch login configuration: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching login configuration:', error);
    throw error;
  }
};

export const exchangeCodeForToken = async (
  code: string,
  redirectUri: string,
  cognitoDomain: string,
  clientId: string
): Promise<{ access_token: string; refresh_token: string }> => {
  try {
    const response = await fetch(`${cognitoDomain}/oauth2/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: `grant_type=authorization_code&client_id=${clientId}&code=${code}&redirect_uri=${encodeURIComponent(redirectUri)}`
    });

    if (!response.ok) {
      throw new Error(`Failed to exchange code for token: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error exchanging code for token:', error);
    throw error;
  }
};

export interface Service {
  id: string;
  name: string;
  description: string;
  maskName: string;
  mobileShortName: string;
  color: string;
  icon: string;
  applicability: string;
  status: boolean;
  url: string;
  created: string;
  updated: string;
}

export interface Action {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  dueDate: string;
  application: string;  // This corresponds to service.maskName
  serviceId: string;    // This corresponds to service.id
  assignedTo: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface ObservationAction {
  id: string;
  application: string;
  applicationDetails?: {
    extraInfo: string;
  };
  applicationId: string;
  actionType: string;
  actionToBeTaken?: string;
  actionTaken?: string;
  maskId: string;
  trackId: string;
  description: string;
  status: string;
  sequence: string;
  prefix: string;
  objectId: string;
  assignedToId: string[];
  submitURL: string;
  created: string;
  updated: string;
  serviceId: string;
  submittedById: string;
  submittedBy?: {
    id: string;
    firstName: string;
    lastName?: string;
    email: string;
    company: string;
    deviceToken?: string;
    arn?: string;
    type: string;
    status: boolean;
    roles: string[];
    created: string;
    updated: string;
  };
  assignedTo?: {
    id: string;
    firstName: string;
    lastName?: string;
    email: string;
  }[];
  dueDate?: string;
  uploads?: string[];
}

export const fetchServices = async (accessToken: string): Promise<Service[]> => {
  try {
    // Use the centralized API service which includes 401 handling
    return await apiService.get('/services');
  } catch (error) {
    console.error('Error fetching services:', error);
    throw error;
  }
};

export const fetchAssignedActions = async (accessToken: string): Promise<Action[]> => {
  try {
    // Use the centralized API service which includes 401 handling
    return await apiService.get('/my-assigned-actions/All');
  } catch (error) {
    console.error('Error fetching assigned actions:', error);
    throw error;
  }
};

export const fetchObservationActions = async (accessToken: string): Promise<ObservationAction[]> => {
  try {
    const uriString = {
      include: [{ relation: "submittedBy" }]
    };
    const url = `/my-assigned-actions/OBS?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;

    // Use the centralized API service which includes 401 handling
    return await apiService.get(url);
  } catch (error) {
    console.error('Error fetching observation actions:', error);
    throw error;
  }
};

export const fetchUserDetails = async (accessToken: string): Promise<UserDetails> => {
  try {
    // Use the centralized API service which includes 401 handling
    return await apiService.get('/users/me');
  } catch (error) {
    console.error('Error fetching user details:', error);
    throw error;
  }
};

export interface User {
  id: string;
  firstName: string;
  lastName?: string;
  email?: string;
  company?: string;
  deviceToken?: string;
  arn?: string;
  type?: string;
  status?: boolean;
  roles?: string[];
  created?: string;
  updated?: string;
  [key: string]: any;
}

// Risk Assessment interfaces
export interface RiskAssessmentTeamMember {
  id: string;
  userId: string;
  riskAssessmentId: string;
  user: {
    id: string;
    firstName: string;
    lastName?: string;
    email?: string;
  };
}

export interface RiskAssessmentResponse {
  id: string;
  maskId: string;
  type: 'Routine' | 'Non Routine' | 'High-Risk Hazard';
  status: 'Pending' | 'Draft' | 'Published';
  created: string;
  publishedDate?: string;
  nextReviewDate?: string;
  department: {
    id: string;
    name: string;
  };
  teamLeader: {
    id: string;
    firstName: string;
    lastName?: string;
  };
  workActivity: {
    id: string;
    name: string;
  };
  raTeamMembers: RiskAssessmentTeamMember[];
}

export const fetchAllUsers = async (accessToken: string): Promise<User[]> => {
  try {
    // Use the centralized API service which includes 401 handling
    return await apiService.get('/users');
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

export const fetchRiskAssessments = async (accessToken: string): Promise<RiskAssessmentResponse[]> => {
  try {
    const uriString = {
      where: {
        $and: [
          { $or: [{ status: 'Pending' }, { status: 'Draft' }, { status: 'Published' }] },
          { $or: [{ type: 'Routine' }, { type: 'Non Routine' }, { type: 'High-Risk Hazard' }] }
        ]
      },
      include: [
        { relation: "department" },
        { relation: "teamLeader" },
        { relation: "workActivity" },
        {
          relation: "raTeamMembers",
          scope: {
            include: [{ relation: "user" }]
          }
        }
      ]
    };

    const url = `/risk-assessments?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

    // Use the centralized API service which includes 401 handling
    return await apiService.get(url);
  } catch (error) {
    console.error('Error fetching risk assessments:', error);
    throw error;
  }
};

export const OBSERVATION_REPORT_URL = `${API_BASE_URL}/observation-reports`;
export const INSPECTIONS_URL = `${API_BASE_URL}/inspections`;
export const CHECKLISTS_URL = `${API_BASE_URL}/checklists`;
export const RISK_ASSESSMENTS_URL = `${API_BASE_URL}/risk-assessments`;

// Inspection interfaces
export interface InspectionAction {
  id: string;
  application: string;
  applicationDetails?: {
    extraInfo?: string;
    [key: string]: any;
  };
  applicationId: string;
  actionType: string;
  actionToBeTaken?: string;
  actionTaken?: string;
  maskId: string;
  trackId: string;
  description: string;
  status: string;
  sequence: string;
  prefix: string;
  counter?: number;
  objectId: string;
  assignedToId: string[];
  submitURL: string;
  created: string;
  updated: string;
  serviceId: string;
  submittedById: string;
  submittedBy?: {
    id: string;
    firstName: string;
    email: string;
    company: string;
    deviceToken?: string;
    arn?: string;
    type: string;
    status: boolean;
    roles: string[];
    created: string;
    updated: string;
  };
  dueDate?: string;
  uploads?: string[];
}

export interface Checklist {
  id: string;
  customId?: string;
  maskId?: string;
  name: string;
  description: string;
  status: string;
  version?: string;
  category?: string;
  curatorId?: string;
  created: string;
  updated: string;
  curator?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface InspectionResponse {
  id: string;
  maskId: string;
  title: string;
  description: string;
  status: string;
  scheduledDate: string;
  completedDate?: string;
  created: string;
  updated: string;
  locationOneId: string;
  locationTwoId: string;
  locationThreeId: string;
  locationFourId: string;
  locationFiveId: string;
  locationSixId: string;
  checklistId: string;
  inspectorId: string;
  assignedById: string;
  locationOne?: {
    id: string;
    name: string;
    created: string;
    updated: string;
  };
  locationTwo?: {
    id: string;
    name: string;
    created: string;
    updated: string;
  };
  locationThree?: {
    id: string;
    name: string;
    created: string;
    updated: string;
  };
  locationFour?: {
    id: string;
    name: string;
    created: string;
    updated: string;
  };
  locationFive?: {
    id: string;
    name: string;
    created: string;
    updated: string;
  };
  locationSix?: {
    id: string;
    name: string;
    created: string;
    updated: string;
  };
  checklist?: Checklist;
  inspector?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  assignedBy?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface ActionOwnerData {
  id: string;
  firstName: string;
  email: string;
}

export interface ObservationResponse {
  id: string;
  maskId: string;
  observationCategory: string;
  observationType: string;
  observationActOrCondition: string;
  description: string;
  comments: string;
  status: string;
  actionTaken: string;
  evidence: string[];
  uploads: string[];
  dueDate: string;
  created: string;
  updated: string;
  rectifiedOnSpot: boolean;
  isQR: boolean;
  actionToBeTaken: string;
  isReviewerRequired: boolean;
  isCustomLocation?: boolean;
  customLocation?: string;
  reporterId: string;
  reviewerId: string;
  actionOwnerId: string;
  actionOwners?: ActionOwnerData;
  multiActionOwners?: ActionOwnerData[];
  locationOneId: string;
  locationTwoId: string;
  locationThreeId: string;
  locationFourId: string;
  locationFiveId: string;
  locationSixId: string;
  locationOne?: {
    id: string;
    name: string;
    created: string;
    updated: string;
  };
  locationTwo?: {
    id: string;
    name: string;
    created: string;
    updated: string;
    locationOneId: string;
  };
  locationThree?: {
    id: string;
    name: string;
    created: string;
    updated: string;
    locationTwoId: string;
  };
  locationFour?: {
    id: string;
    name: string;
    created: string;
    updated: string;
    locationThreeId: string;
  };
  locationFive?: {
    id: string;
    name: string;
    created: string;
    updated: string;
    locationFourId: string;
  };
  locationSix?: {
    id: string;
    name: string;
    created: string;
    updated: string;
    locationFiveId: string;
  };
  reporter?: {
    id: string;
    firstName: string;
    lastName?: string;
    email: string;
    company: string;
    deviceToken?: string;
    arn?: string;
    type: string;
    status: boolean;
    roles: string[];
    created: string;
    updated: string;
  };
  actionOwner?: {
    id: string;
    firstName: string;
    lastName?: string;
    email: string;
  };
  reviewer?: {
    id: string;
    firstName: string;
    lastName?: string;
    email: string;
  };
  observationActions?: ObservationAction[];
}

export const fetchObservations = async (accessToken: string): Promise<ObservationResponse[]> => {
  try {
    const uriString = {
      include: [
        { relation: "locationOne" },
        { relation: "locationTwo" },
        { relation: "locationThree" },
        { relation: "locationFour" },
        { relation: "locationFive" },
        { relation: "locationSix" },
        { relation: "reporter" },
        { relation: "actionOwner" },
        { relation: "multiActionOwners" },
        { relation: "reviewer" },
        { relation: "observationActions" },
      ]
    };

    const url = `/observation-reports?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;

    // Use the centralized API service which includes 401 handling
    return await apiService.get(url);
  } catch (error) {
    console.error('Error fetching observations:', error);
    throw error;
  }
};

// Inspection API functions
export const fetchInspectionActions = async (accessToken: string): Promise<InspectionAction[]> => {
  try {
    const uriString = {
      include: [{ relation: "submittedBy" }]
    };
    const url = `/my-assigned-actions/INS?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;

    // Use the centralized API service which includes 401 handling
    return await apiService.get(url);
  } catch (error) {
    console.error('Error fetching inspection actions:', error);
    throw error;
  }
};

export const fetchScheduledInspections = async (accessToken: string): Promise<InspectionResponse[]> => {
  try {
    const uriString = {
      where: { status: 'Scheduled' },
      include: [
        { relation: "locationOne" },
        { relation: "locationTwo" },
        { relation: "locationThree" },
        { relation: "locationFour" },
        { relation: "locationFive" },
        { relation: "locationSix" },
        { relation: "checklist" },
        { relation: "inspector" },
        { relation: "assignedBy" }
      ]
    };

    const url = `/inspections?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;

    // Use the centralized API service which includes 401 handling
    return await apiService.get(url);
  } catch (error) {
    console.error('Error fetching scheduled inspections:', error);
    throw error;
  }
};

export const fetchCompletedInspections = async (accessToken: string): Promise<InspectionResponse[]> => {
  try {
    const uriString = {
      where: {
        status: { neq: 'Scheduled' }
      },
      include: [
        { relation: "locationOne" },
        { relation: "locationTwo" },
        { relation: "locationThree" },
        { relation: "locationFour" },
        { relation: "locationFive" },
        { relation: "locationSix" },
        { relation: "checklist" },
        { relation: "inspector" },
        { relation: "assignedBy" }
      ]
    };

    const url = `/inspections?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;

    // Use the centralized API service which includes 401 handling
    return await apiService.get(url);
  } catch (error) {
    console.error('Error fetching completed inspections:', error);
    throw error;
  }
};

export const fetchChecklists = async (accessToken: string): Promise<Checklist[]> => {
  try {
    const uriString = {
      include: [{ relation: "curator" }]
    };

    const url = `${CHECKLISTS_URL}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch checklists: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching checklists:', error);
    throw error;
  }
};

export const updateChecklist = async (accessToken: string, checklistId: string, checklistData: any): Promise<any> => {
  try {
    // Use the centralized API service which includes 401 handling
    return await apiService.patch(`/checklists/${checklistId}`, checklistData);
  } catch (error) {
    console.error('Error updating checklist:', error);
    throw error;
  }
};

export const curateChecklist = async (accessToken: string, checklistId: string, curationData: any): Promise<any> => {
  try {
    // Use the centralized API service which includes 401 handling
    return await apiService.patch(`/checklists/${checklistId}/curate`, curationData);
  } catch (error) {
    console.error('Error curating checklist:', error);
    throw error;
  }
};

export const createChecklist = async (accessToken: string, checklistData: any): Promise<any> => {
  try {
    // Use the centralized API service which includes 401 handling
    return await apiService.post('/checklists', checklistData);
  } catch (error) {
    console.error('Error creating checklist:', error);
    throw error;
  }
};

export const archiveChecklist = async (accessToken: string, checklistId: string): Promise<any> => {
  try {
    // Use the centralized API service which includes 401 handling
    return await apiService.patch(`/checklists/${checklistId}`, { status: "Archived" });
  } catch (error) {
    console.error('Error archiving checklist:', error);
    throw error;
  }
};

// Fetch detailed observation with action history
export const fetchObservationWithActions = async (observationId: string, accessToken: string): Promise<ObservationResponse> => {
  try {
    const uriString = {
      include: [
        { relation: "locationOne" },
        { relation: "locationTwo" },
        { relation: "locationThree" },
        { relation: "locationFour" },
        { relation: "locationFive" },
        { relation: "locationSix" },
        { relation: "reporter" },
        { relation: "actionOwner" },
        { relation: "multiActionOwners" },
        { relation: "reviewer" },
        {
          relation: "observationActions",
          scope: {
            include: [
              { relation: "submittedBy" },
              { relation: "assignedTo" }
            ],
            order: "created ASC"
          }
        },
      ]
    };

    const url = `${OBSERVATION_REPORT_URL}/${observationId}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch observation details: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching observation details:', error);
    throw error;
  }
};

// Fetch detailed inspection with all relations
export const fetchInspectionWithDetails = async (inspectionId: string, accessToken: string): Promise<InspectionResponse> => {
  try {
    const uriString = {
      include: [
        { relation: "locationOne" },
        { relation: "locationTwo" },
        { relation: "locationThree" },
        { relation: "locationFour" },
        { relation: "locationFive" },
        { relation: "locationSix" },
        { relation: "checklist" },
        { relation: "inspector" },
        { relation: "assignedBy" }
      ]
    };

    const url = `${INSPECTIONS_URL}/${inspectionId}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;

    // Use the centralized API service which includes 401 handling
    return await apiService.get(url);
  } catch (error) {
    console.error('Error fetching inspection with details:', error);
    throw error;
  }
};
