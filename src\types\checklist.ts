export type ContentMode = "communicate" | "evaluate" | "observe" | "document";

export interface ContentComponent {
  id: string;
  type: string;
  title: string;
  description?: string;
  required?: boolean;
  position: number;
  options?: string[];
  placeholder?: string;
  maxLength?: number;
  minValue?: number;
  maxValue?: number;
  step?: number;
  multiple?: boolean;
  accept?: string;
  rows?: number;
  cols?: number;
  format?: string;
  validation?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    min?: number;
    max?: number;
  };
}

export interface DroppedItem {
  id: string;
  type: string;
  data: ContentComponent;
}

export interface ChecklistTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  version: string;
  items: DroppedItem[];
  created: string;
  updated: string;
}
