import React, { useEffect, useState, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { format, isValid } from 'date-fns';
import { fetchAllUsers, User, InspectionResponse } from '@/services/api';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import ImageComponent from '@/components/common/ImageComponent';
import apiService from '@/services/apiService';
import { useReactToPrint } from 'react-to-print';
import {
  Calendar,
  User as UserIcon,
  MapPin,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Building,
  PenTool,
  TextCursor,
  Heading1,
  Heading2,
  Type,
  ClipboardList,
  Printer
} from 'lucide-react';

interface InspectionDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  inspection: InspectionResponse | null;
}

const InspectionDetailsModal: React.FC<InspectionDetailsModalProps> = ({
  open,
  onOpenChange,
  inspection
}) => {
  const [users, setUsers] = useState<User[]>([]);
  const [logo, setLogo] = useState<string>('');
  const { accessToken } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();
  const printRef = useRef<HTMLDivElement>(null);

  const handlePrint = useReactToPrint({
    contentRef: printRef,
    documentTitle: `Inspection_${inspection?.maskId}_${format(new Date(), 'yyyyMMdd_HHmm')}`,
    onAfterPrint: () => {
      toast({
        title: "Print Successful",
        description: "The inspection report has been sent to the printer.",
        variant: "default",
      });
    },
    onPrintError: (error) => {
      console.error('Print error:', error);
      toast({
        title: "Print Failed",
        description: "An error occurred while printing. Please try again.",
        variant: "destructive",
      });
    },
  });

  useEffect(() => {
    if (open && accessToken) {
      getAllUsers();
      getFetchLogo();
    }
  }, [open, accessToken]);

  const getAllUsers = async () => {
    try {
      if (accessToken) {
        const response = await fetchAllUsers(accessToken);
        setUsers(response);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const getFetchLogo = async () => {
    try {
      const logoName = localStorage.getItem('logo');
      if (logoName) {
        const logoUrl = await apiService.get(`/files/presigned-url/${logoName}`);
        setLogo(logoUrl);
      }
    } catch (error) {
      console.error('Error fetching logo:', error);
    }
  };

  const getName = (id: string) => {
    const user = users.find(user => user.id === id);
    return user?.firstName || '';
  };

  const formatDate = (dateString: string | undefined | null) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      return isValid(date) ? format(date, 'dd-MM-yyyy') : "N/A";
    } catch {
      return "N/A";
    }
  };

  if (!inspection) return null;

  const locationDisplay = [
    inspection.locationOne,
    inspection.locationTwo,
    inspection.locationThree,
    inspection.locationFour,
    inspection.locationFive,
    inspection.locationSix
  ]
    .filter(location => location?.name)
    .map(location => location.name)
    .join(' > ') || "N/A";

  const getStatusBadge = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return <Badge className="bg-green-500 text-white"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>;
      case 'in progress':
        return <Badge className="bg-blue-500 text-white"><Clock className="w-3 h-3 mr-1" />In Progress</Badge>;
      case 'scheduled':
        return <Badge className="bg-yellow-500 text-white"><Calendar className="w-3 h-3 mr-1" />Scheduled</Badge>;
      default:
        return <Badge variant="secondary"><AlertCircle className="w-3 h-3 mr-1" />{status || 'Unknown'}</Badge>;
    }
  };

  const renderChecklistComponents = () => {
    // Handle both new format (components array) and legacy format (direct value object)
    let components: any[] = [];

    // Access the inspection value data - try multiple possible locations
    const inspectionValue = (inspection as any).value ||
      (inspection as any).checklist?.value ||
      (inspection as any).checklistValue ||
      {};

    if (inspectionValue) {
      if (Array.isArray(inspectionValue)) {
        // Legacy format - array of groups
        components = inspectionValue.map((group: any, index: number) => ({
          id: `legacy-${index}`,
          type: 'checkpoint-group',
          data: group,
          position: index
        }));
      } else if (inspectionValue.components && Array.isArray(inspectionValue.components)) {
        // New format - components array
        components = inspectionValue.components;
      } else if (typeof inspectionValue === 'object') {
        // Check if this is an object with numbered keys (0, 1, 2, etc.)
        const keys = Object.keys(inspectionValue);
        const hasNumberedKeys = keys.some(key => !isNaN(Number(key)));

        if (hasNumberedKeys) {
          // Object with numbered keys - extract components
          components = Object.values(inspectionValue).filter((item: any) =>
            item && typeof item === 'object' && item.type && item.id
          );
        }
      }
    }

    // Sort components by position
    components.sort((a, b) => (a.position || 0) - (b.position || 0));

    if (components.length === 0) {
      return (
        <Card>
          <CardContent className="p-6 text-center text-muted-foreground">
            <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No checklist data available for this inspection.</p>
          </CardContent>
        </Card>
      );
    }

    return components.map((component: any, index: number) => {
      const componentData = component.data || component;

      // Handle different component types
      if (component.type === 'header') {
        return (
          <div key={component.id || `header-${index}`} className="mb-6">
            <div className="flex items-center gap-3 mb-2">
              <Heading1 className="w-6 h-6 text-blue-500" />
              <h2 className="text-xl font-bold text-gray-800">{componentData.text}</h2>
            </div>
            <div className="h-px bg-gradient-to-r from-blue-500 via-blue-300 to-transparent"></div>
          </div>
        );
      }

      // Handle Section Header Component
      if (component.type === 'section-header') {
        return (
          <div key={component.id || `section-${index}`} className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Heading2 className="w-5 h-5 text-indigo-500" />
              <h3 className="text-lg font-semibold text-indigo-700">{componentData.text}</h3>
            </div>
            <div className="h-px bg-indigo-200"></div>
          </div>
        );
      }

      if (component.type === 'text-body') {
        return (
          <Card key={component.id || `text-${index}`} className="mb-4 border-l-4 border-l-gray-400">
            <CardContent className="pt-4">
              <div className="flex items-start gap-3">
                <Type className="w-5 h-5 text-gray-500 mt-1" />
                <div className="prose prose-sm max-w-none">
                  <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                    {componentData.content}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      }

      // Handle Text Input Component
      if (component.type === 'text-input') {
        return (
          <Card key={component.id || `text-input-${index}`} className="mb-4 border-l-4 border-l-blue-400">
            <CardContent className="pt-4">
              <div className="flex items-start gap-3">
                <TextCursor className="w-5 h-5 text-blue-500 mt-1" />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium text-gray-800">{componentData.label}</span>
                    {componentData.required && <span className="text-red-500 text-sm">*</span>}
                  </div>
                  <div className="bg-gray-50 border rounded-md p-3">
                    <p className="text-sm text-gray-700">
                      {componentData.textValue || <span className="text-gray-400 italic">No response provided</span>}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      }

      // Handle Individual Checkpoint Component
      if (component.type === 'checkpoint') {
        return (
          <Card key={component.id || `checkpoint-${index}`} className="mb-4 border-l-4 border-l-green-400">
            <CardContent className="pt-4">
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-1" />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-3">
                    <span className="font-medium text-gray-800">{componentData.text}</span>
                    {componentData.required && <span className="text-red-500 text-sm">*</span>}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-600">Response:</span>
                      <Badge variant={componentData.selected === 'Yes' ? 'default' : componentData.selected === 'No' ? 'destructive' : 'secondary'}>
                        {componentData.selected || 'N/A'}
                      </Badge>
                    </div>

                    {componentData.remarks && (
                      <div className="bg-gray-50 border rounded-md p-3">
                        <span className="text-sm font-medium text-gray-600">Remarks:</span>
                        <p className="text-sm text-gray-700 mt-1">{componentData.remarks}</p>
                      </div>
                    )}

                    {componentData.actionToBeTaken && (
                      <div className="bg-red-50 border border-red-200 rounded-md p-3">
                        <span className="text-sm font-medium text-red-600">Action Required:</span>
                        <p className="text-sm text-red-700 mt-1">{componentData.actionToBeTaken}</p>
                        {componentData.dueDate && (
                          <p className="text-xs text-red-600 mt-1">Due: {formatDate(componentData.dueDate)}</p>
                        )}
                        {componentData.assignee && (
                          <p className="text-xs text-red-600 mt-1">Assignee: {getName(componentData.assignee)}</p>
                        )}
                      </div>
                    )}

                    {componentData.uploads && componentData.uploads.length > 0 && (
                      <div className="space-y-2">
                        <span className="text-sm font-medium text-gray-600">Evidence:</span>
                        <div className="grid grid-cols-3 gap-2">
                          {componentData.uploads.map((file: string, i: number) => (
                            <div key={i} className="border rounded-md overflow-hidden">
                              <ImageComponent fileName={file} size="100" name={false} />
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      }

      // Handle Date Component
      if (component.type === 'date') {
        return (
          <Card key={component.id || `date-${index}`} className="mb-4 border-l-4 border-l-purple-400">
            <CardContent className="pt-4">
              <div className="flex items-start gap-3">
                <Calendar className="w-5 h-5 text-purple-500 mt-1" />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium text-gray-800">{componentData.label}</span>
                    {componentData.required && <span className="text-red-500 text-sm">*</span>}
                  </div>
                  <div className="bg-purple-50 border border-purple-200 rounded-md p-3">
                    <p className="text-sm text-purple-700">
                      {componentData.selectedDate ? formatDate(componentData.selectedDate) : 'No date selected'}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      }

      // Handle Signature Component
      if (component.type === 'sign') {
        return (
          <Card key={component.id || `sign-${index}`} className="mb-4 border-l-4 border-l-orange-400">
            <CardContent className="pt-4">
              <div className="flex items-start gap-3">
                <PenTool className="w-5 h-5 text-orange-500 mt-1" />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium text-gray-800">{componentData.label}</span>
                    {componentData.required && <span className="text-red-500 text-sm">*</span>}
                  </div>
                  <div className="bg-orange-50 border border-orange-200 rounded-md p-3">
                    {componentData.signature ? (
                      <div className="text-center">
                        <p className="text-xs text-orange-600 mb-2">Digital Signature</p>
                        <div className="inline-block border-2 border-orange-200 rounded-lg p-2 bg-white">
                          <div className='w-96 h-48'>
                            <ImageComponent fileName={componentData.signature} size="200" name={false} />
                          </div>
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-orange-600 text-center">No signature provided</p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      }

      // Handle checkpoint groups
      if (component.type === 'checkpoint-group' || componentData.label) {
        const groupTitle = componentData.title || componentData.label;
        const isChecked = componentData.isChecked || componentData.groupAnswer === 'Yes';
        const checkpoints = componentData.checkpoints || componentData.questions || [];

        return (
          <Card key={component.id || `group-${index}`} className="mb-4 border-l-4 border-l-blue-500">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <ClipboardList className="w-6 h-6 text-blue-500" />
                  <CardTitle className="text-lg">{groupTitle}</CardTitle>
                </div>
                <Badge variant={isChecked ? 'default' : 'secondary'}>
                  {isChecked ? 'Completed' : 'Not Completed'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              {isChecked ? (
                <div className="space-y-4">
                  {checkpoints.map((checkpoint: any, qIndex: number) => (
                    <div key={`q-${qIndex}`} className="border-l-4 border-blue-200 pl-4 py-2 bg-blue-50/30 rounded-r-md">
                      <h4 className="font-medium text-sm mb-2">{checkpoint.text || checkpoint.label}</h4>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <span className="text-xs font-medium text-gray-600">Response:</span>
                          <Badge variant={checkpoint.selected === 'Yes' ? 'default' : checkpoint.selected === 'No' ? 'destructive' : 'secondary'} className="text-xs">
                            {checkpoint.selected || 'N/A'}
                          </Badge>
                        </div>
                        {checkpoint.remarks && (
                          <div className="bg-white border rounded-md p-2">
                            <span className="text-xs font-medium text-gray-600">Remarks:</span>
                            <p className="text-xs text-gray-700 mt-1">{checkpoint.remarks}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
                  <p className="text-sm text-gray-600">This checkpoint group was not completed.</p>
                </div>
              )}
            </CardContent>
          </Card>
        );
      }

      // Default fallback for unknown component types
      return (
        <Card key={component.id || `unknown-${index}`} className="mb-4 border-l-4 border-l-gray-400">
          <CardContent className="pt-4">
            <div className="flex items-center gap-3">
              <AlertCircle className="w-5 h-5 text-gray-500" />
              <div>
                <p className="font-medium text-gray-800">Component Type: {component.type}</p>
                <p className="text-sm text-gray-600">This component type is displayed in a simplified format.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      );
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[1000px] max-h-[90vh] overflow-y-auto"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl font-bold">
                {inspection.title || `Inspection ${inspection.maskId}`}
              </DialogTitle>
              <DialogDescription className="text-base mt-1">
                ID: {inspection.maskId} • {formatDate(inspection.scheduledDate)}
              </DialogDescription>
            </div>
            <div className="flex items-center space-x-3 no-print">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrint}
                className="bg-green-50 hover:bg-green-100 border-green-300 text-green-700"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print Report
              </Button>
              {getStatusBadge(inspection.status)}
            </div>
          </div>
        </DialogHeader>

        {/* Printable Content */}
        <div ref={printRef} className="print-content">
          {/* Print-only header */}
          <div className="print-only" style={{ display: 'none' }}>
            <div className="text-center mb-8">
              {logo && (
                <img 
                  src={logo} 
                  alt="Company Logo" 
                  style={{ width: '80px', height: 'auto', margin: '0 auto 20px' }}
                />
              )}
              <h1 style={{ fontSize: '28px', fontWeight: 'bold', color: '#1f2937', margin: '0 0 10px' }}>
                INSPECTION REPORT
              </h1>
              <p style={{ fontSize: '16px', color: '#6b7280', fontStyle: 'italic', margin: '0 0 20px' }}>
                Inspection ID: {inspection.maskId}
              </p>
              <div style={{ height: '2px', background: '#3b82f6', margin: '20px 0' }}></div>
            </div>
          </div>

          <div className="space-y-6">
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <Calendar className="w-8 h-8 text-blue-500" />
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Scheduled</p>
                      <p className="text-lg font-semibold">{formatDate(inspection.scheduledDate)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="w-8 h-8 text-green-500" />
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Completed</p>
                      <p className="text-lg font-semibold">{formatDate(inspection.actualCompletionDate)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <UserIcon className="w-8 h-8 text-purple-500" />
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Inspector</p>
                      <p className="text-lg font-semibold">{inspection.inspector?.firstName || "N/A"}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <Building className="w-8 h-8 text-orange-500" />
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Status</p>
                      <p className="text-lg font-semibold">{inspection.status || "N/A"}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Description */}
            {inspection.description && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="w-5 h-5" />
                    <span>Description</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm leading-relaxed">{inspection.description}</p>
                </CardContent>
              </Card>
            )}

            {/* Location & Checklist Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="w-5 h-5" />
                  <span>Location & Checklist</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Location</label>
                  <p className="text-sm mt-1">{locationDisplay}</p>
                </div>
                <Separator />
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Checklist ID</label>
                  <p className="text-sm mt-1">{inspection.checklist?.customId || "N/A"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Checklist Category</label>
                  <p className="text-sm mt-1">{inspection.checklist?.category || "N/A"}</p>
                </div>
              </CardContent>
            </Card>

            {/* Checklist Results */}
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
                <FileText className="w-5 h-5" />
                <span>Checklist Results</span>
              </h3>
              {renderChecklistComponents()}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};



export default InspectionDetailsModal;
