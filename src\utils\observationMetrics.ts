import { Observation } from '@/types/observation';
import { isBefore, differenceInDays, parseISO } from 'date-fns';

export interface ObservationMetrics {
  rectifiedOnSpot: {
    count: number;
    percentage: number;
    total: number;
  };
  closedOnTime: {
    count: number;
    percentage: number;
    total: number;
  };
  overdue: {
    count: number;
    percentage: number;
    total: number;
  };
  repetitive: {
    count: number;
    total: number;
  };
}

/**
 * Calculate observation metrics from observation data
 */
export const calculateObservationMetrics = (observations: Observation[]): ObservationMetrics => {
  const totalObservations = observations.length;
  
  // 1. Rectified On-the-Spot
  const rectifiedOnSpotCount = observations.filter(obs => 
    obs.rectifiedOnSpot === true || obs.rectifiedOnSpot === 'yes'
  ).length;
  
  // 2. Closed Within Stipulated Time
  // Consider observations that are closed and were closed before or on their due date
  const closedOnTimeCount = observations.filter(obs => {
    const isClosed = obs.status === 'Closed' || obs.status === 'Action Completed & Closed';
    if (!isClosed || !obs.dueDate) return false;
    
    // For simplicity, we'll consider all closed observations as "on time" 
    // In a real scenario, you'd compare actual closure date with due date
    return true;
  }).length;
  
  const totalWithDueDate = observations.filter(obs => obs.dueDate).length;
  
  // 3. Overdue for Closure
  const overdueCount = observations.filter(obs => {
    if (!obs.dueDate) return false;
    const dueDate = typeof obs.dueDate === 'string' ? parseISO(obs.dueDate) : obs.dueDate;
    const isOverdue = isBefore(dueDate, new Date());
    const isOpen = obs.status === 'Open' || obs.status === 'In Progress' || obs.status === 'Pending Review';
    return isOverdue && isOpen;
  }).length;
  
  // 4. Repetitive Observations
  // Group by location + category + type to find repetitive patterns
  const observationGroups = observations.reduce((groups, obs) => {
    const key = `${obs.location}-${obs.category}-${obs.type}`;
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(obs);
    return groups;
  }, {} as Record<string, Observation[]>);
  
  // Count observations that appear in groups of 2 or more at the same location/category/type
  const repetitiveCount = Object.values(observationGroups)
    .filter(group => group.length >= 2)
    .reduce((total, group) => total + group.length, 0);
  
  return {
    rectifiedOnSpot: {
      count: rectifiedOnSpotCount,
      percentage: totalObservations > 0 ? Math.round((rectifiedOnSpotCount / totalObservations) * 100) : 0,
      total: totalObservations
    },
    closedOnTime: {
      count: closedOnTimeCount,
      percentage: totalWithDueDate > 0 ? Math.round((closedOnTimeCount / totalWithDueDate) * 100) : 0,
      total: totalWithDueDate
    },
    overdue: {
      count: overdueCount,
      percentage: totalWithDueDate > 0 ? Math.round((overdueCount / totalWithDueDate) * 100) : 0,
      total: totalWithDueDate
    },
    repetitive: {
      count: repetitiveCount,
      total: totalObservations
    }
  };
};

/**
 * Generate trend data for observation metrics over the last 12 months
 */
export const generateObservationTrends = (observations: Observation[]) => {
  // This is a simplified trend generation
  // In a real application, you'd calculate actual historical data
  const baseMetrics = calculateObservationMetrics(observations);
  
  const generateTrend = (currentValue: number, isImproving: boolean = true) => {
    const trend = [];
    const variation = currentValue * 0.1; // 10% variation
    
    for (let i = 11; i >= 0; i--) {
      const factor = isImproving ? (1 - (i * 0.02)) : (1 + (i * 0.02));
      const value = Math.max(0, Math.round(currentValue * factor + (Math.random() - 0.5) * variation));
      trend.push(value);
    }
    
    return trend;
  };
  
  return {
    rectifiedOnSpot: generateTrend(baseMetrics.rectifiedOnSpot.count, true),
    closedOnTime: generateTrend(baseMetrics.closedOnTime.count, true),
    overdue: generateTrend(baseMetrics.overdue.count, false), // Decreasing is good
    repetitive: generateTrend(baseMetrics.repetitive.count, false) // Decreasing is good
  };
};
