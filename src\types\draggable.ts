
import { ReactNode } from 'react';

export type ContentMode = 'communicate' | 'feedback';

export interface ComponentBase {
  id: string;
  type: string;
  position: number;
  required?: boolean;
  allowFromLibrary?: boolean;
}

// Communicate component types
export interface ImageComponent extends ComponentBase {
  type: 'image';
  src?: string;
  alt?: string;
  fileSize?: number;
}

export interface VideoComponent extends ComponentBase {
  type: 'video';
  src?: string;
  title?: string;
  fileSize?: number;
}

export interface YouTubeComponent extends ComponentBase {
  type: 'youtube';
  videoId?: string;
  url?: string;
}

export interface WebLinkComponent extends ComponentBase {
  type: 'weblink';
  url?: string;
  title?: string;
  thumbnail?: string;
}

export interface TextComponent extends ComponentBase {
  type: 'text';
  content?: string;
}

export interface HeaderComponent extends ComponentBase {
  type: 'header';
  text?: string;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
}

export interface SectionHeaderComponent extends ComponentBase {
  type: 'section-header';
  text?: string;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
}

export interface TextBodyComponent extends ComponentBase {
  type: 'text-body';
  content?: string;
}

export interface AudioComponent extends ComponentBase {
  type: 'audio';
  src?: string;
  title?: string;
  fileSize?: number;
  isRecorded?: boolean;
}

export interface AttachmentComponent extends ComponentBase {
  type: 'attachment';
  src?: string;
  fileName?: string;
  fileType?: string;
  fileSize?: number;
}

export interface EmbedComponent extends ComponentBase {
  type: 'embed';
  code?: string;
}

export interface ScormComponent extends ComponentBase {
  type: 'scorm';
  src?: string;
  fileName?: string;
  fileSize?: number;
}

export interface WebGLComponent extends ComponentBase {
  type: 'webgl';
  src?: string;
  fileName?: string;
  fileSize?: number;
}

// Feedback component types
export interface MCQComponent extends ComponentBase {
  type: 'mcq';
  question?: string;
  options?: { id: string; text: string }[];
  allowMultiple?: boolean;
  required: boolean;
}





export interface FeedbackVideoComponent extends ComponentBase {
  type: 'feedback-video';
  label?: string;
  required: boolean;
  allowFromLibrary: boolean;
  src?: string;
  fileSize?: number;
}

export interface FeedbackAudioComponent extends ComponentBase {
  type: 'feedback-audio';
  label?: string;
  required: boolean;
  allowFromLibrary: boolean;
  src?: string;
  fileSize?: number;
  isRecorded?: boolean;
}



export interface DropdownComponent extends ComponentBase {
  type: 'dropdown';
  label?: string;
  options?: { id: string; text: string }[];
  required: boolean;
}

export interface SignComponent extends ComponentBase {
  type: 'sign';
  label?: string;
  required: boolean;
}



export interface StarComponent extends ComponentBase {
  type: 'star';
  label?: string;
  maxStars?: number;
  required: boolean;
}

export interface DateComponent extends ComponentBase {
  type: 'date';
  label?: string;
  required: boolean;
}

export interface TextInputComponent extends ComponentBase {
  type: 'text-input';
  label?: string;
  placeholder?: string;
  required: boolean;
}









export interface CheckpointComponent extends ComponentBase {
  type: 'checkpoint';
  text?: string;
  description?: string;
  required: boolean;
}

export interface CheckpointGroupComponent extends ComponentBase {
  type: 'checkpoint-group';
  title?: string;
  description?: string;
  checkpoints?: { id: string; text: string; description?: string }[];
  required: boolean;
}

export type CommunicateComponentType =
  | HeaderComponent
  | SectionHeaderComponent
  | TextBodyComponent;

export type FeedbackComponentType =
  | MCQComponent
  | FeedbackVideoComponent
  | FeedbackAudioComponent
  | DropdownComponent
  | SignComponent
  | StarComponent
  | DateComponent
  | TextInputComponent
  | CheckpointComponent
  | CheckpointGroupComponent;

export type ContentComponent = CommunicateComponentType | FeedbackComponentType;

export interface DraggableItemProps {
  type: string;
  icon: ReactNode;
  label: string;
  description?: string;
  onDragStart: (type: string) => void;
}

export interface DroppedItem {
  id: string;
  type: string;
  data: ContentComponent;
}
